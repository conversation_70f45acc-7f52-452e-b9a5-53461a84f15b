import 'package:flutter_test/flutter_test.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/services/fishing_spot_service.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// 生成Mock类
@GenerateMocks([FishingSpotService])
import 'fishing_spots_filter_test.mocks.dart';

void main() {
  group('FishingSpotViewModel Filter Tests', () {
    late FishingSpotViewModel viewModel;
    late MockFishingSpotService mockService;

    setUp(() {
      mockService = MockFishingSpotService();
      viewModel = FishingSpotViewModel(mockService);
    });

    test('applyFilters should set filterType to null when filter is "全部"', () {
      // Act
      viewModel.applyFilters(
        filter: "全部",
        fishTypes: [],
        hasFacilities: false,
        hasParking: false,
      );

      // Assert
      expect(viewModel.filterType, isNull);
    });

    test('applyFilters should keep filterType when filter is not "全部"', () {
      // Act
      viewModel.applyFilters(
        filter: "官方认证",
        fishTypes: [],
        hasFacilities: false,
        hasParking: false,
      );

      // Assert
      expect(viewModel.filterType, equals("官方认证"));
    });

    test('applyFilters should set fishTypes to null when list is empty', () {
      // Act
      viewModel.applyFilters(
        filter: "全部",
        fishTypes: [],
        hasFacilities: false,
        hasParking: false,
      );

      // Assert
      expect(viewModel.selectedFishTypes, isNull);
    });

    test('applyFilters should keep fishTypes when list is not empty', () {
      // Act
      viewModel.applyFilters(
        filter: "全部",
        fishTypes: ["鲤鱼", "草鱼"],
        hasFacilities: false,
        hasParking: false,
      );

      // Assert
      expect(viewModel.selectedFishTypes, equals(["鲤鱼", "草鱼"]));
    });

    test('applyFilters should set hasFacilities to null when false', () {
      // Act
      viewModel.applyFilters(
        filter: "全部",
        fishTypes: [],
        hasFacilities: false,
        hasParking: false,
      );

      // Assert
      expect(viewModel.hasFacilities, isNull);
    });

    test('applyFilters should keep hasFacilities when true', () {
      // Act
      viewModel.applyFilters(
        filter: "全部",
        fishTypes: [],
        hasFacilities: true,
        hasParking: false,
      );

      // Assert
      expect(viewModel.hasFacilities, equals(true));
    });

    test('applyFilters should handle null fishTypes parameter', () {
      // Act
      viewModel.applyFilters(
        filter: "全部",
        fishTypes: null,
        hasFacilities: false,
        hasParking: false,
      );

      // Assert
      expect(viewModel.selectedFishTypes, isNull);
    });
  });
}
