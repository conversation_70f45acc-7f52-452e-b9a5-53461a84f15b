import 'dart:async';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/services/user_service.dart';

class BaseAuthViewModel extends BaseViewModel {
  final UserService userService;
  final SharedPreferences sharedPreferences;

  // Stream for notifying auth state changes
  final StreamController<void> _authStateChangedController =
      StreamController<void>.broadcast();

  User? _currentUser;

  BaseAuthViewModel({
    required this.userService,
    required this.sharedPreferences,
  });

  User? get currentUser => _currentUser;

  Stream<void> get authStateChanged => _authStateChangedController.stream;

  bool isUserLoggedIn() {
    return userService.getAccessToken() != null;
  }

  int getCurrentUserId() {
    _currentUser = userService.getCachedUser();
    return _currentUser?.id ?? 0;
  }

  Future<void> getCurrentUser() async {
    setBusy(true);
    try {
      _currentUser = await userService.getUserProfile(getCurrentUserId());
      await userService.cacheUser(_currentUser!);
    } catch (error) {
      // Handle error, possibly logging out user if unauthorized
      if (error.toString().contains('401')) {
        await logout();
      }
    }
    setBusy(false);
  }

  Future<void> logout() async {
    setBusy(true);
    await userService.logout();
    _currentUser = null;
    _notifyAuthStateChanged();
    setBusy(false);
  }

  Future<void> storeUserAndToken(User user, String token) async {
    await userService.cacheUser(user);
    await userService.cacheToken(token);
    _currentUser = user;
    _notifyAuthStateChanged();
  }

  void _notifyAuthStateChanged() {
    _authStateChangedController.add(null);
  }

  bool checkAndPromptLogin(BuildContext context) {
    if (!isUserLoggedIn()) {
      showLoginPrompt(context);
      return false;
    }
    return true;
  }

  void showLoginPrompt(BuildContext context) {
    // Implementation of login prompt dialog
    // This can be moved to a separate UI utility class
  }

  @override
  void dispose() {
    _authStateChangedController.close();
    super.dispose();
  }
}
