import 'package:flutter/foundation.dart';
import 'package:user_app/api/fishing_spot_api.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/create_spot_dto.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_page_vo.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_detail_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';

import 'package:user_app/services/search_service.dart';

/// Service class for handling fishing spot business logic
class FishingSpotService {
  final FishingSpotApi _api;
  final SearchService?
      _searchService; // Optional because we may not always need it

  /// Constructor that accepts an optional API instance and search service
  FishingSpotService(this._api, [this._searchService]);

  /// Fetches a paginated list of fishing spots as summary (preferred for lists)
  ///
  /// [page] - Page number (0-based) for pagination
  /// [size] - Number of items per page
  /// [filterType] - Filter by type (official, user, free, paid)
  /// [fishTypes] - List of fish types to filter by
  /// [hasFacilities] - Filter for spots with facilities
  Future<SpotSummaryPageVo> getFishingSpotsAsSummary({
    int page = 0,
    int size = 10,
    String? filterType,
    List<String>? fishTypes,
    bool? hasFacilities,
  }) async {
    try {
      final result = await _api.getFishingSpotsAsSummary(
        page: page,
        size: size,
        filterType: filterType,
        fishTypes: fishTypes,
        hasFacilities: hasFacilities,
      );

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching fishing spots summary: $e');
      }
      rethrow;
    }
  }

  /// Fetches a paginated list of fishing spots with optional filtering
  /// (Legacy method for backward compatibility)
  ///
  /// [page] - Page number (0-based) for pagination
  /// [size] - Number of items per page
  /// [filterType] - Filter by type (official, user, free, paid)
  /// [fishTypes] - List of fish types to filter by
  /// [hasFacilities] - Filter for spots with facilities
  Future<FishingSpotPageVo> getFishingSpots({
    int page = 0,
    int size = 10,
    String? filterType,
    List<String>? fishTypes,
    bool? hasFacilities,
  }) async {
    try {
      final result = await _api.getFishingSpots(
        page: page,
        size: size,
        filterType: filterType,
        fishTypes: fishTypes,
        hasFacilities: hasFacilities,
      );

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching fishing spots: $e');
      }
      rethrow;
    }
  }

  /// Gets detailed information for a specific fishing spot (legacy method)
  ///
  /// [id] - ID of the fishing spot to fetch
  Future<FishingSpotVo> getFishingSpotDetail(int id) async {
    try {
      if (kDebugMode) {
        print('🌐 [Service] Calling API getFishingSpotDetail for ID: $id');
      }
      final result = await _api.getFishingSpotDetail(id);
      if (kDebugMode) {
        print('✅ [Service] API getFishingSpotDetail completed for ID: $id');
      }
      return result;
    } catch (e) {
      if (kDebugMode) {
        print(
            '❌ [Service] Error fetching fishing spot detail for ID: $id, error: $e');
      }
      rethrow;
    }
  }

  /// Gets detailed information for a specific fishing spot using optimized SpotDetailVO
  ///
  /// [id] - ID of the fishing spot to fetch
  Future<SpotDetailVo> getFishingSpotDetailVO(int id) async {
    try {
      if (kDebugMode) {
        print('🌐 [Service] Calling API getFishingSpotDetailVO for ID: $id');
      }
      final result = await _api.getFishingSpotDetailVO(id);
      if (kDebugMode) {
        print('✅ [Service] API getFishingSpotDetailVO completed for ID: $id');
      }
      return result;
    } catch (e) {
      if (kDebugMode) {
        print(
            '❌ [Service] Error fetching fishing spot detail VO for ID: $id, error: $e');
      }
      rethrow;
    }
  }

  /// Fetches fishing spots optimized for map display (lightweight data)
  /// 使用基于位置的查询，需要提供中心点坐标
  ///
  /// [latitude] - 中心点纬度 (必需)
  /// [longitude] - 中心点经度 (必需)
  /// [radius] - 搜索半径，单位公里 (默认50km)
  /// [page] - Page number (0-based)
  /// [size] - Number of items per page
  /// [filterType] - Type filter (暂不支持，保留接口兼容性)
  /// [fishTypes] - List of fish types to filter by (暂不支持，保留接口兼容性)
  /// [hasFacilities] - Filter by facilities availability (暂不支持，保留接口兼容性)
  /// [hasParking] - Filter by parking availability (暂不支持，保留接口兼容性)
  /// [bounds] - Optional map bounds (暂不支持，保留接口兼容性)
  Future<SpotMapPageVo> getFishingSpotsForMap({
    required double latitude,
    required double longitude,
    double radius = 50.0,
    int page = 0,
    int size = 50, // 地图可以加载更多数据，因为数据量小
    String? filterType,
    List<String>? fishTypes,
    bool? hasFacilities,
    bool? hasParking,
    Map<String, double>? bounds, // {north, south, east, west}
  }) async {
    try {
      if (kDebugMode) {
        print('🌐 [Service] Calling API getFishingSpotsForMap with params:');
        print('   - latitude: $latitude, longitude: $longitude');
        print('   - radius: $radius, page: $page, size: $size');
        print('   - filterType: $filterType (暂不支持)');
        print('   - fishTypes: $fishTypes (暂不支持)');
        print('   - hasFacilities: $hasFacilities (暂不支持)');
        print('   - hasParking: $hasParking (暂不支持)');
        print('   - bounds: $bounds (暂不支持)');
      }

      // 调用 API 获取地图数据 - 使用 /nearby/map 接口
      final result = await _api.getFishingSpotsForMap(
        latitude: latitude,
        longitude: longitude,
        radius: radius,
        page: page,
        size: size,
        filterType: filterType,
        fishTypes: fishTypes,
        hasFacilities: hasFacilities,
        hasParking: hasParking,
        bounds: bounds,
      );

      if (kDebugMode) {
        print('✅ [Service] API getFishingSpotsForMap completed');
        print('   - Total elements: ${result.totalElements}');
        print('   - Current page: ${result.number}');
        print('   - Content size: ${result.content.length}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Service] Error fetching fishing spots for map: $e');
      }
      // 返回空的分页响应而不是抛出异常，这样UI层可以正确处理
      return const SpotMapPageVo(
        number: 0,
        size: 0,
        totalPages: 0,
        totalElements: 0,
        first: true,
        last: true,
        content: [],
      );
    }
  }

  /// Checks in to a fishing spot (records a visit)
  ///
  /// [spotId] - ID of the fishing spot to check in to
  /// Returns true if check-in was successful
  Future<bool> checkinFishingSpot(int spotId) async {
    try {
      return await _api.checkinFishingSpot(spotId);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking in to fishing spot: $e');
      }
      rethrow;
    }
  }

  /// 从DTO创建一个钓点
  ///
  /// 参数 [createSpotDto] - 包含钓点所有信息的DTO对象
  /// 返回 - 创建成功的钓点ID
  Future<int> createSpotFromDto(CreateSpotDto createSpotDto) async {
    try {
      // 创建与后端SpotCreateDto结构一致的数据
      final Map<String, dynamic> spotData = {
        // 1. 创建spot对象，包含基本信息
        'spot': {
          'name': createSpotDto.name,
          'description': createSpotDto.description,
          'latitude': createSpotDto.latitude,
          'longitude': createSpotDto.longitude,
          'address': createSpotDto.address,
          'province': createSpotDto.province,
          'city': createSpotDto.city,
          'county': createSpotDto.county,
        },

        // 2. 添加其他顶级属性
        'fish_type_names': createSpotDto.fishTypeNames,
        'extra_fish_types': createSpotDto.extraFishTypes,
        'extra_facilities': createSpotDto.extraFacilities,
        'image_urls': createSpotDto.imageUrls,
        'has_facilities': createSpotDto.hasFacilities,
        'is_paid': createSpotDto.isPaid,
        'is_official': createSpotDto.isOfficial,
        'visibility': createSpotDto.visibility,
      };

      // 3. 处理设施列表 - 只包含已选择的设施
      if (createSpotDto.hasFacilities) {
        spotData['facilities'] = createSpotDto.facilities
            .where((f) => f.isSelected)
            .map((f) => {
                  'name': f.name,
                  'icon': f.icon,
                  'description': f.description,
                })
            .toList();
      } else {
        spotData['facilities'] = [];
      }

      // 4. 处理价格列表 - 只在收费时包含
      if (createSpotDto.isPaid) {
        spotData['prices'] = createSpotDto.prices
            .map((p) => {
                  'price_type': p.priceType,
                  'price_type_name': p.priceTypeName,
                  'fish_type': {
                    'id': p.fishTypeId,
                    'name': p.fishTypeName,
                  },
                  'price': p.price,
                  'hours': p.hours,
                  'description': p.description,
                })
            .toList();
      } else {
        spotData['prices'] = [];
      }

      // 5. 处理认证文档
      spotData['certification_documents'] =
          createSpotDto.certificationDocuments.map((doc) => doc.url).toList();

      // 调用API创建钓点
      return await _api.createFishingSpot(spotData);
    } catch (e) {
      if (kDebugMode) {
        print('Error creating fishing spot: $e');
      }
      rethrow;
    }
  }

  /// 获取用户最近签到的钓点列表
  ///
  /// [page] - 页码 (0-based)
  /// [pageSize] - 每页数量
  Future<List<FishingSpotVo>> getRecentCheckins({
    int page = 0,
    int pageSize = 10,
  }) async {
    try {
      return await _api.getRecentCheckins(page: page, pageSize: pageSize);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching recent checkins: $e');
      }
      rethrow;
    }
  }

  /// 获取用户收藏的钓点列表
  ///
  /// [page] - 页码 (0-based)
  /// [pageSize] - 每页数量
  Future<List<FishingSpotVo>> getFavorites({
    int page = 0,
    int pageSize = 10,
  }) async {
    try {
      return await _api.getFavorites(page: page, pageSize: pageSize);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching favorites: $e');
      }
      rethrow;
    }
  }

  /// 获取用户创建的钓点列表
  ///
  /// [page] - 页码 (0-based)
  /// [pageSize] - 每页数量
  Future<List<FishingSpotVo>> getMyCreatedSpots({
    int page = 0,
    int pageSize = 10,
  }) async {
    try {
      return await _api.getMyCreatedSpots(page: page, pageSize: pageSize);
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching my created spots: $e');
      }
      rethrow;
    }
  }

  /// 获取附近的钓点列表
  ///
  /// [latitude] - 纬度
  /// [longitude] - 经度
  /// [page] - 页码 (0-based)
  /// [pageSize] - 每页数量
  /// [radiusKm] - 搜索半径（公里）
  Future<List<FishingSpotVo>> getNearbySpots({
    required double latitude,
    required double longitude,
    int page = 0,
    int pageSize = 10,
    double radiusKm = 50,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint(
            'Service: Getting nearby spots at lat=$latitude, lng=$longitude, page=$page, size=$pageSize, radius=$radiusKm');
      }

      // 检查坐标是否有效
      if (latitude == 0 && longitude == 0) {
        if (kDebugMode) {
          debugPrint(
              'Warning: Both latitude and longitude are 0, which is likely invalid');
        }
      }

      final spots = await _api.getNearbySpots(
        latitude: latitude,
        longitude: longitude,
        page: page,
        pageSize: pageSize,
        radiusKm: radiusKm,
      );

      if (kDebugMode) {
        debugPrint('Service: Received ${spots.length} nearby spots');
        if (spots.isEmpty) {
          debugPrint(
              'Service: No nearby spots found within $radiusKm km radius');
        }
      }

      return spots;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error fetching nearby spots: $e');
      }
      // 返回空列表而不是抛出异常，这样UI层可以正确处理
      return [];
    }
  }

  /// 获取附近钓点的地图数据（直接返回SpotMapPageVo格式）
  ///
  /// [latitude] - 纬度
  /// [longitude] - 经度
  /// [page] - 页码 (0-based)
  /// [pageSize] - 每页数量
  /// [radiusKm] - 搜索半径（公里）
  Future<SpotMapPageVo> getNearbyMapSpots({
    required double latitude,
    required double longitude,
    int page = 0,
    int pageSize = 50,
    double radiusKm = 50,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint(
            'Service: Getting nearby map spots at lat=$latitude, lng=$longitude, page=$page, size=$pageSize, radius=$radiusKm');
      }

      // 检查坐标是否有效
      if (latitude == 0 && longitude == 0) {
        if (kDebugMode) {
          debugPrint(
              'Warning: Both latitude and longitude are 0, which is likely invalid');
        }
      }

      final mapPage = await _api.getNearbyMapSpots(
        latitude: latitude,
        longitude: longitude,
        page: page,
        pageSize: pageSize,
        radiusKm: radiusKm,
      );

      if (kDebugMode) {
        debugPrint('Service: Received nearby map spots page:');
        debugPrint('  - Page: ${mapPage.number}/${mapPage.totalPages}');
        debugPrint('  - Total: ${mapPage.totalElements}');
        debugPrint('  - Current: ${mapPage.content.length}');
      }

      return mapPage;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error fetching nearby map spots: $e');
      }
      // 返回空的分页响应而不是抛出异常，这样UI层可以正确处理
      return const SpotMapPageVo(
        number: 0,
        size: 0,
        totalPages: 0,
        totalElements: 0,
        first: true,
        last: true,
        content: [],
      );
    }
  }

  /// 搜索钓点
  ///
  /// [query] - 搜索关键词
  /// [page] - 页码 (0-based)
  /// [pageSize] - 每页数量
  Future<List<FishingSpotVo>> searchFishingSpots({
    required String query,
    int page = 0,
    int pageSize = 10,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    try {
      // 使用新的搜索服务 (如果可用)
      if (_searchService != null && query.isNotEmpty) {
        // 使用新的综合搜索API搜索钓点
        final searchResult = await _searchService.search(
          query,
          type: 'spot', // 只搜索钓点
          page: page + 1, // 新API使用1-based分页
          size: pageSize,
          province: null, // 可以根据需要设置
          city: null,
        );

        if (kDebugMode) {
          print(
              'Using new search service for fishing spots query: $query, found ${searchResult.total} results');
        }

        // 将搜索结果转换为FishingSpotVo
        // 注意：这里需要后端SearchService返回钓点数据，或者我们退回到API搜索
        return await _api.searchFishingSpots(
          query: query,
          page: page,
          pageSize: pageSize,
          latitude: latitude,
          longitude: longitude,
          radiusKm: radiusKm,
        );
      }

      // 退回到后端API搜索
      return await _api.searchFishingSpots(
        query: query,
        page: page,
        pageSize: pageSize,
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error searching fishing spots: $e');
      }
      rethrow;
    }
  }

  /// 收藏钓点
  ///
  /// [spotId] - 需要收藏的钓点ID
  /// 返回收藏是否成功
  Future<bool> favoriteFishingSpot(int spotId) async {
    try {
      return await _api.favoriteFishingSpot(spotId);
    } catch (e) {
      if (kDebugMode) {
        print('Error favoriting fishing spot: $e');
      }
      rethrow;
    }
  }

  /// 取消收藏钓点
  ///
  /// [spotId] - 需要取消收藏的钓点ID
  /// 返回取消收藏是否成功
  Future<bool> unfavoriteFishingSpot(int spotId) async {
    try {
      return await _api.unfavoriteFishingSpot(spotId);
    } catch (e) {
      if (kDebugMode) {
        print('Error unfavoriting fishing spot: $e');
      }
      rethrow;
    }
  }

  /// 检查钓点是否已收藏
  ///
  /// [spotId] - 需要检查的钓点ID
  /// 返回钓点是否已被收藏
  Future<bool> isFavorite(int spotId) async {
    try {
      return await _api.isFavorite(spotId);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if fishing spot is favorite: $e');
      }
      return false; // 查询失败默认返回未收藏
    }
  }

  /// 获取用户创建的钓点列表
  ///
  /// [userId] - 用户ID
  /// [page] - 页码 (0-based)
  /// [size] - 每页大小
  /// 返回用户创建的钓点列表
  Future<List<FishingSpotVo>> getUserCreatedSpots({
    required int userId,
    int page = 0,
    int size = 10,
  }) async {
    try {
      debugPrint('🔍 [FishingSpotService] 获取用户 $userId 创建的钓点, page: $page, size: $size');
      final spots = await _api.getUserCreatedSpots(userId, page, size);
      debugPrint('✅ [FishingSpotService] 获取到 ${spots.length} 个用户钓点');
      return spots;
    } catch (e) {
      debugPrint('❌ [FishingSpotService] 获取用户钓点失败: $e');
      if (kDebugMode) {
        print('Error getting user created fishing spots: $e');
      }
      rethrow;
    }
  }
}
