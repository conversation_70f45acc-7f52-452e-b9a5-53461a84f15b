import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_detail_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/widgets/fishing_spot_details_content.dart';
import 'package:user_app/models/user.dart';

class FishingSpotDetailPage extends StatefulWidget {
  final int spotId;
  final FishingSpotVo? initialSpot;

  const FishingSpotDetailPage({
    super.key,
    required this.spotId,
    this.initialSpot,
  });

  @override
  State<FishingSpotDetailPage> createState() => _FishingSpotDetailPageState();
}

class _FishingSpotDetailPageState extends State<FishingSpotDetailPage> {
  SpotDetailVo? spotDetail;
  FishingSpotVo? spot; // 保留用于兼容性
  bool isLoading = false;
  String? error;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    if (widget.initialSpot != null) {
      spot = widget.initialSpot;
    } else {
      _loadSpotDetail();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadSpotDetail() async {
    debugPrint('=== FishingSpotDetailPage _loadSpotDetail 调试信息 ===');
    debugPrint('请求的spotId: ${widget.spotId}');
    debugPrint('spotId类型: ${widget.spotId.runtimeType}');
    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      final fishingSpotService = context.read<FishingSpotService>();
      debugPrint('开始调用 fishingSpotService.getFishingSpotDetailVO(${widget.spotId})');
      final loadedSpotDetail = await fishingSpotService.getFishingSpotDetailVO(widget.spotId);
      debugPrint('API调用成功，获得钓点详情数据: ${loadedSpotDetail.id}');
      debugPrint('createdBy: ${loadedSpotDetail.createdBy}');
      debugPrint('creator: ${loadedSpotDetail.creator}');
      
      if (mounted) {
        setState(() {
          spotDetail = loadedSpotDetail;
          spot = loadedSpotDetail.toFishingSpotVo(); // 转换为兼容格式
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('API调用失败: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      if (mounted) {
        setState(() {
          error = e.toString();
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(spot?.name ?? '钓点详情'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (error != null) {
      return SingleChildScrollView(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                '加载失败',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSpotDetail,
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    if (spot == null) {
      return const Center(
        child: Text('钓点不存在'),
      );
    }

    return FishingSpotDetailsContent(
      spot: spot!,
      scrollController: _scrollController,
      onCreatorTap: _navigateToUserProfile,
    );
  }

  void _navigateToUserProfile(User user) {
    final route = '${AppRoutes.profile}/${user.id}';
    debugPrint('🚀 [FishingSpotDetail] 导航到用户主页: $route');
    debugPrint('🚀 [FishingSpotDetail] 用户信息: 姓名=${user.name}, ID=${user.id}');
    context.push(route);
  }
}