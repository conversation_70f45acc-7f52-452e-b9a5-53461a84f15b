import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/widgets/facility_item.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/screens/route_planning_page.dart';
import 'package:user_app/widgets/user_circle_avatar.dart';

class FishingSpotDetailsContent extends StatelessWidget {
  final FishingSpotVo spot;
  final ScrollController scrollController;
  final VoidCallback? onNavigationPressed;
  final VoidCallback? onViewDynamicPressed;
  final VoidCallback? onCheckinPressed;
  final Function(User)? onCreatorTap;

  const FishingSpotDetailsContent({
    super.key,
    required this.spot,
    required this.scrollController,
    this.onNavigationPressed,
    this.onViewDynamicPressed,
    this.onCheckinPressed,
    this.onCreatorTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return CustomScrollView(
      controller: scrollController,
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Flat drag handle
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),

                // Header with name and verification badge
                Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        spot.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.w700,
                          letterSpacing: -0.5,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (spot.isOfficial || spot.verificationLevel > 0)
                        Row(
                          children: [
                            if (spot.isOfficial)
                              _buildFlatBadge(
                                icon: Icons.verified,
                                label: '官方认证',
                                color: colorScheme.primary,
                              )
                            else if (spot.verificationLevel > 0)
                              _buildFlatBadge(
                                icon: Icons.thumb_up,
                                label: '用户推荐 ${spot.verificationLevel}/3',
                                color: Colors.orange,
                              ),
                          ],
                        ),
                    ],
                  ),
                ),

                // Creator information (只在有完整creator对象时显示)
                if (spot.creator != null)
                  _buildCreatorSection(spot.creator!)
                else if (spot.createdBy != null && kDebugMode)
                  // 在debug模式下显示创建者ID
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.withOpacity(0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info, size: 16, color: Colors.orange.shade700),
                          const SizedBox(width: 8),
                          Text(
                            '创建者ID: ${spot.createdBy} (需要后端API支持)',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Address section
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    children: [
                      Icon(
                        Icons.location_on_rounded,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          spot.address,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                            height: 1.3,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Images gallery
                if (spot.images != null && spot.images!.isNotEmpty)
                  _buildFlatImageGallery(spot.images!),

                const SizedBox(height: 16),

                // Description
                if (spot.description != null && spot.description!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Text(
                      spot.description!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                        height: 1.4,
                      ),
                    ),
                  ),

                // Fish types section
                if (spot.fishTypeList.isNotEmpty ||
                    spot.extraFishTypesList.isNotEmpty)
                  _buildFlatDetailsSection(
                    context: context,
                    title: '鱼类品种',
                    icon: Icons.catching_pokemon,
                    color: Colors.blue,
                    child: _buildFlatChipsWrap(
                      items: [
                        ...spot.fishTypeList.map((fish) => fish.name),
                        ...spot.extraFishTypesList,
                      ],
                      color: Colors.blue,
                    ),
                  ),

                // Best seasons section
                _buildFlatDetailsSection(
                  context: context,
                  title: '最佳季节',
                  icon: Icons.calendar_today_rounded,
                  color: Colors.orange,
                  child: _buildFlatChipsWrap(
                    items: spot.seasons,
                    color: Colors.orange,
                    currentValue: _getCurrentSeason(),
                  ),
                ),

                // Facilities section
                if (spot.hasFacilities)
                  _buildFlatDetailsSection(
                    context: context,
                    title: '场地设施',
                    icon: Icons.restaurant_rounded,
                    color: Colors.green,
                    child: _buildFlatFacilitiesContent(spot),
                  ),

                // Price section
                _buildFlatDetailsSection(
                  context: context,
                  title: '价格信息',
                  icon: Icons.payments_rounded,
                  color: Colors.purple,
                  child: _buildFlatPricingContent(spot),
                ),

                // Stats section
                _buildFlatDetailsSection(
                  context: context,
                  title: '数据统计',
                  icon: Icons.bar_chart_rounded,
                  color: Colors.indigo,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildFlatStat(
                          icon: Icons.remove_red_eye_rounded,
                          value: spot.visitorCount.toString(),
                          label: '访问',
                          color: Colors.indigo,
                        ),
                        Container(
                          height: 32,
                          width: 1,
                          color: Colors.grey.shade300,
                        ),
                        _buildFlatStat(
                          icon: Icons.person_rounded,
                          value: spot.checkinCount.toString(),
                          label: '签到',
                          color: Colors.indigo,
                        ),
                        Container(
                          height: 32,
                          width: 1,
                          color: Colors.grey.shade300,
                        ),
                        _buildFlatStat(
                          icon: Icons.star_rounded,
                          value: spot.rating.toStringAsFixed(1),
                          label: '评分',
                          color: Colors.amber,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          if (spot.latitude != 0 && spot.longitude != 0) {
                            final lngLat = LngLat(spot.longitude, spot.latitude);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => RoutePlanningPage(
                                  address: spot.address,
                                  destination: lngLat,
                                ),
                              ),
                            );
                          }
                        },
                        icon: const Icon(Icons.directions_rounded, size: 18),
                        label: const Text('导航'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          elevation: 0,
                        ),
                      ),
                    ),
                    if (onViewDynamicPressed != null) ...[
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            onViewDynamicPressed?.call();
                          },
                          icon: const Icon(Icons.article_outlined, size: 18),
                          label: const Text('查看动态'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.blue,
                            side: BorderSide(color: Colors.blue.withOpacity(0.5)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 12),

                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      onCheckinPressed?.call();
                    },
                    icon:
                        const Icon(Icons.check_circle_outline_rounded, size: 18),
                    label: const Text('签到打卡'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.green,
                      side: BorderSide(color: Colors.green.withOpacity(0.5)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Flat image gallery without heavy shadows
  Widget _buildFlatImageGallery(List<String> images) {
    return SizedBox(
      height: 200,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: PageView.builder(
          itemCount: images.length,
          itemBuilder: (context, index) {
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  images[index],
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported_rounded,
                          color: Colors.grey,
                          size: 32,
                        ),
                      ),
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // Flat section header with icon
  Widget _buildFlatDetailsSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  size: 16,
                  color: color,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          child,
        ],
      ),
    );
  }

  // Flat chip wrapper for categories, fish types, seasons
  Widget _buildFlatChipsWrap({
    required List<String> items,
    required Color color,
    String? currentValue,
  }) {
    return Wrap(
      spacing: 6,
      runSpacing: 6,
      children: items.map((item) {
        final bool isHighlighted = currentValue != null && item == currentValue;
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color:
                isHighlighted ? color.withOpacity(0.15) : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            item,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isHighlighted ? color : Colors.grey.shade700,
            ),
          ),
        );
      }).toList(),
    );
  }

  // Flat pricing section
  Widget _buildFlatPricingContent(FishingSpotVo spot) {
    if (!spot.isPaid) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(
                Icons.money_off_rounded,
                color: Colors.green,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '免费钓场',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
                Text(
                  '无需支付任何费用',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    // Simple price display if no detailed prices available
    if (spot.prices.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.purple.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(
                Icons.payments_rounded,
                color: Colors.purple,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '收费钓场',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.purple,
                  ),
                ),
                Text(
                  '¥${spot.price?.toStringAsFixed(0) ?? "未知"}/天',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    // Detailed price display when prices list is available
    return Column(
      children: spot.prices.map((priceItem) {
        String title = priceItem.priceTypeName;
        if (priceItem.fishType != null) {
          title += ' (${priceItem.fishType?.name})';
        }
        String durationText =
            priceItem.hours != null ? '${priceItem.hours}小时' : '每天';

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      durationText,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '¥${priceItem.price.toStringAsFixed(0)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.purple,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // Flat facilities section
  Widget _buildFlatFacilitiesContent(FishingSpotVo spot) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Wrap(
        spacing: 12,
        runSpacing: 12,
        children: spot.facilities.isNotEmpty
            ? spot.facilities.map((facility) {
                return _buildFlatFacilityItem(
                  icon: FacilityItem.getIconFromString(facility.icon),
                  label: facility.name,
                  color: Colors.green,
                );
              }).toList()
            : [
                _buildFlatFacilityItem(
                  icon: Icons.wc_rounded,
                  label: '卫生间',
                  color: Colors.green,
                ),
                _buildFlatFacilityItem(
                  icon: Icons.local_parking_rounded,
                  label: '停车场',
                  color: Colors.green,
                ),
              ],
      ),
    );
  }

  // Helper method to get current season
  String _getCurrentSeason() {
    final month = DateTime.now().month;
    if (month >= 3 && month <= 5) {
      return '春';
    } else if (month >= 6 && month <= 8) {
      return '夏';
    } else if (month >= 9 && month <= 11) {
      return '秋';
    } else {
      return '冬';
    }
  }

  // Flat badge widget
  Widget _buildFlatBadge({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  // Flat stat widget
  Widget _buildFlatStat({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade800,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  // Flat facility item widget
  Widget _buildFlatFacilityItem({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade700,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // Build creator section
  Widget _buildCreatorSection(User creator) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              debugPrint('👤 [FishingSpotDetailsContent] 用户头像被点击: ${creator.name} (ID: ${creator.id})');
              if (onCreatorTap != null) {
                onCreatorTap!(creator);
              } else {
                debugPrint('⚠️ [FishingSpotDetailsContent] onCreatorTap 回调为空');
              }
            },
            child: UserCircleAvatar(
              avatarUrl: creator.avatarUrl,
              radius: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      creator.name ?? '匿名用户',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '发布者',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                if (creator.introduce != null && creator.introduce!.isNotEmpty)
                  Text(
                    creator.introduce!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          Icon(
            Icons.chevron_right,
            size: 16,
            color: Colors.grey.shade400,
          ),
        ],
      ),
    );
  }
}
