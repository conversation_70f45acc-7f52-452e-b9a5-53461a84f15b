import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/models/moment/user_fans_attentions_request.dart';

class FollowedUsersPage extends StatefulWidget {
  const FollowedUsersPage({super.key});

  @override
  State<FollowedUsersPage> createState() => _FollowedUsersPageState();
}

class _FollowedUsersPageState extends State<FollowedUsersPage> {
  final UserService _userService = getIt<UserService>();
  List<User> followedUsers = [];
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadFollowedUsers();
  }

  Future<void> _loadFollowedUsers() async {
    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      // 先获取当前用户信息
      final currentUser = _userService.getCachedUser();
      if (currentUser == null) {
        setState(() {
          error = '请先登录';
        });
        return;
      }

      // 调用真实的API获取关注用户列表
      final request = UserFansAttentionsRequest(
        userId: currentUser.id,
        pageNum: 0,
        pageSize: 50, // 获取更多用户
      );
      
      final response = await _userService.getAttentions(request);
      setState(() {
        followedUsers = response.records;
      });
    } catch (e) {
      setState(() {
        error = '加载关注用户失败: $e';
      });
      print('加载关注用户失败: $e'); // 添加日志便于调试
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选择聊天对象'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFollowedUsers,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              error!,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadFollowedUsers,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (followedUsers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '暂无关注的用户',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: followedUsers.length,
      itemBuilder: (context, index) {
        final user = followedUsers[index];
        return _buildUserItem(user);
      },
    );
  }

  Widget _buildUserItem(User user) {
    return ListTile(
      leading: CircleAvatar(
        radius: 24,
        backgroundImage: user.avatarUrl != null && user.avatarUrl!.isNotEmpty
            ? NetworkImage(user.avatarUrl!)
            : null,
        child: user.avatarUrl == null || user.avatarUrl!.isEmpty
            ? Text(user.name.isNotEmpty ? user.name[0] : '?')
            : null,
      ),
      title: Text(
        user.name,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            user.title,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          if (user.introduce != null && user.introduce!.isNotEmpty)
            Text(
              user.introduce!,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.person, color: Colors.blue),
            onPressed: () => _viewUserProfile(user),
            tooltip: '查看主页',
          ),
          IconButton(
            icon: const Icon(Icons.chat, color: Colors.green),
            onPressed: () => _startChatWithUser(user),
            tooltip: '发起聊天',
          ),
        ],
      ),
      onTap: () => _startChatWithUser(user),
    );
  }

  void _viewUserProfile(User user) {
    context.push('/user_profile', extra: {'userId': user.id});
  }

  void _startChatWithUser(User user) {
    context.pop(); // 返回聊天列表
    context.push('/chat_detail', extra: {
      'conversationID': 'c2c_${user.id}',
      'conversationType': 1, // C2C
      'showName': user.name,
      'userID': user.id.toString(),
    });
  }
}