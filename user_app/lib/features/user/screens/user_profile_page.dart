import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/core/di/injection.dart';

class UserProfilePage extends StatefulWidget {
  final int userId;

  const UserProfilePage({
    super.key,
    required this.userId,
  });

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage>
    with SingleTickerProviderStateMixin {
  final UserService _userService = getIt<UserService>();
  late TabController _tabController;
  
  User? user;
  bool isLoading = true;
  String? error;
  bool isFollowing = false;
  bool isFollowLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserProfile();
    _checkFollowStatus();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      // 调用真实的API获取用户详情
      final userData = await _userService.getUserProfile(widget.userId);
      setState(() {
        user = userData;
      });
    } catch (e) {
      setState(() {
        error = '加载用户信息失败: $e';
      });
      print('加载用户信息失败: $e'); // 添加日志便于调试
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _checkFollowStatus() async {
    try {
      // 调用真实的API检查关注状态
      final status = await _userService.isFollowingUser(widget.userId);
      setState(() {
        isFollowing = status;
      });
    } catch (e) {
      print('检查关注状态失败: $e');
    }
  }

  Future<void> _toggleFollow() async {
    setState(() {
      isFollowLoading = true;
    });

    try {
      if (isFollowing) {
        // 调用真实的API取消关注
        await _userService.unfollowUser(widget.userId);
        setState(() {
          isFollowing = false;
        });
        _showSnackBar('已取消关注');
      } else {
        // 调用真实的API关注用户
        await _userService.followUser(widget.userId);
        setState(() {
          isFollowing = true;
        });
        _showSnackBar('关注成功');
      }
    } catch (e) {
      _showSnackBar('操作失败: $e');
    } finally {
      setState(() {
        isFollowLoading = false;
      });
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Scaffold(
        appBar: null,
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('用户主页'),
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                error!,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadUserProfile,
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    if (user == null) {
      return const Scaffold(
        body: Center(
          child: Text('用户不存在'),
        ),
      );
    }

    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          SliverAppBar(
            expandedHeight: 300,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: _buildProfileHeader(),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: _showMoreOptions,
              ),
            ],
          ),
          SliverPersistentHeader(
            delegate: _SliverAppBarDelegate(
              TabBar(
                controller: _tabController,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Colors.blue,
                tabs: const [
                  Tab(text: '动态'),
                  Tab(text: '钓点'),
                  Tab(text: '收藏'),
                ],
              ),
            ),
            pinned: true,
          ),
        ];
      },
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMomentsTab(),
          _buildSpotsTab(),
          _buildFavoritesTab(),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const SizedBox(height: 50), // 为AppBar留空间
          CircleAvatar(
            radius: 50,
            backgroundImage: user!.avatarUrl != null && user!.avatarUrl!.isNotEmpty
                ? NetworkImage(user!.avatarUrl!)
                : null,
            child: user!.avatarUrl == null || user!.avatarUrl!.isEmpty
                ? Text(
                    user!.name.isNotEmpty ? user!.name[0] : '?',
                    style: const TextStyle(fontSize: 24),
                  )
                : null,
          ),
          const SizedBox(height: 16),
          Text(
            user!.name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            user!.title,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          if (user!.province != null || user!.city != null)
            const SizedBox(height: 4),
          if (user!.province != null || user!.city != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                Text(
                  '${user!.province ?? ''}${user!.city ?? ''}${user!.county ?? ''}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('动态', user!.momentCount ?? 0),
              _buildStatItem('关注', user!.attentionCount ?? 0),
              _buildStatItem('粉丝', user!.followCount ?? 0),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: isFollowLoading ? null : _toggleFollow,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isFollowing ? Colors.grey : Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: isFollowLoading
                      ? const SizedBox(
                          height: 16,
                          width: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(isFollowing ? '已关注' : '关注'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _startChat,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('发消息'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildMomentsTab() {
    return const Center(
      child: Text('用户动态列表'),
    );
  }

  Widget _buildSpotsTab() {
    return const Center(
      child: Text('用户钓点列表'),
    );
  }

  Widget _buildFavoritesTab() {
    return const Center(
      child: Text('用户收藏列表'),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.report),
                title: const Text('举报'),
                onTap: () {
                  Navigator.pop(context);
                  _showSnackBar('举报功能待开发');
                },
              ),
              ListTile(
                leading: const Icon(Icons.block),
                title: const Text('拉黑'),
                onTap: () {
                  Navigator.pop(context);
                  _showSnackBar('拉黑功能待开发');
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _startChat() {
    context.push('/chat_detail', extra: {
      'conversationID': 'c2c_${user!.id}',
      'conversationType': 1, // C2C
      'showName': user!.name,
      'userID': user!.id.toString(),
    });
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}