import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/utils/date_time_util.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/view_models/other_profile_page_view_model.dart';
import 'package:user_app/widgets/follow_button.dart';
import 'package:user_app/widgets/user_circle_avatar.dart';

class OtherProfilePage extends StatelessWidget {
  final num userId;

  const OtherProfilePage({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<OtherProfilePageViewModel>(
      create: (context) =>
          getIt<OtherProfilePageViewModel>(param1: userId)..init(),
      child: _OtherProfilePageContent(),
    );
  }
}

class _OtherProfilePageContent extends StatefulWidget {
  @override
  State<_OtherProfilePageContent> createState() =>
      _OtherProfilePageContentState();
}

class _OtherProfilePageContentState extends State<_OtherProfilePageContent> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: context.read<OtherProfilePageViewModel>().scrollController,
        slivers: [
          const SliverAppBar(
            floating: true,
            snap: true,
          ),
          SliverList(
            delegate: SliverChildListDelegate([
              const _BuildProfileInfo(),
              const _BuildStatistics(),
              const _BuildActionButtons(),
              const _BuildTabBar(),
            ]),
          ),
          _BuildContentGrid(),
        ],
      ),
    );
  }
}

class _BuildProfileInfo extends StatelessWidget {
  const _BuildProfileInfo();

  @override
  Widget build(BuildContext context) {
    return Selector<OtherProfilePageViewModel, ({User? user})>(
      selector: (context, viewModel) => (user: viewModel.user,),
      builder: (context, data, child) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              UserCircleAvatar(avatarUrl: data.user?.avatarUrl, radius: 50),
              const SizedBox(height: 10),
              Text(
                data.user?.name ?? '',
                style:
                    const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 5),
              Text(
                data.user?.introduce ?? '',
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.grey),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _BuildStatistics extends StatelessWidget {
  const _BuildStatistics();

  @override
  Widget build(BuildContext context) {
    return Selector<OtherProfilePageViewModel, User?>(
      selector: (context, viewModel) => viewModel.user,
      builder: (context, user, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            InkWell(
              child: _BuildStatItem(
                  count: user?.followCount.toString() ?? '0', label: '粉丝'),
              onTap: () {
                context.push(AppRoutes.fansPage.replaceFirst(
                    ':userId',
                    context
                        .read<OtherProfilePageViewModel>()
                        .userId
                        .toString()));
              },
            ),
            Container(width: 1, height: 25, color: Colors.grey),
            InkWell(
              child: _BuildStatItem(
                  count: user?.attentionCount.toString() ?? '0', label: '关注'),
              onTap: () {
                context.push(AppRoutes.attentionsPage.replaceFirst(
                    ':userId',
                    context
                        .read<OtherProfilePageViewModel>()
                        .userId
                        .toString()));
              },
            ),
            Container(width: 1, height: 25, color: Colors.grey),
            _BuildStatItem(
                count: user?.momentCount.toString() ?? '0', label: '动态'),
          ],
        );
      },
    );
  }
}

class _BuildStatItem extends StatelessWidget {
  final String count;
  final String label;

  const _BuildStatItem({required this.count, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(count,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
        Text(label, style: const TextStyle(color: Colors.grey)),
      ],
    );
  }
}

class _BuildActionButtons extends StatelessWidget {
  const _BuildActionButtons();

  @override
  Widget build(BuildContext context) {
    final currentUserId = context.read<AuthViewModel>().getCurrentUserId();
    final userId = context.read<OtherProfilePageViewModel>().userId;

    if (currentUserId == userId) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          FollowButton(userId: userId),
          ElevatedButton(
            onPressed: () {
              context.read<OtherProfilePageViewModel>().navigateToChat(context);
            },
            child: const Text('私信'),
          ),
        ],
      ),
    );
  }
}

class _BuildTabBar extends StatelessWidget {
  const _BuildTabBar();

  @override
  Widget build(BuildContext context) {
    return Consumer<OtherProfilePageViewModel>(
      builder: (context, viewModel, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => viewModel.switchTab(ProfileTab.moments),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: viewModel.currentTab == ProfileTab.moments
                              ? Theme.of(context).primaryColor
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.article_outlined,
                          size: 20,
                          color: viewModel.currentTab == ProfileTab.moments
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '动态',
                          style: TextStyle(
                            color: viewModel.currentTab == ProfileTab.moments
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                            fontWeight: viewModel.currentTab == ProfileTab.moments
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => viewModel.switchTab(ProfileTab.spots),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: viewModel.currentTab == ProfileTab.spots
                              ? Theme.of(context).primaryColor
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.place_outlined,
                          size: 20,
                          color: viewModel.currentTab == ProfileTab.spots
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '钓点',
                          style: TextStyle(
                            color: viewModel.currentTab == ProfileTab.spots
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                            fontWeight: viewModel.currentTab == ProfileTab.spots
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _BuildContentGrid extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<OtherProfilePageViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.currentTab == ProfileTab.moments) {
          return _buildMomentsGrid(viewModel);
        } else {
          return _buildSpotsGrid(viewModel);
        }
      },
    );
  }

  Widget _buildMomentsGrid(OtherProfilePageViewModel viewModel) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (BuildContext context, int index) {
            if (index < viewModel.moments.length) {
              return _ListItem(
                thumbnail: _BuildImageItem(moment: viewModel.moments[index]),
                content: viewModel.moments[index].content ?? '',
                tags: null, // tag field no longer exists
                location: viewModel.moments[index].location ?? '未知地点',
                publishDate: DateTimeUtil.formatTime(
                    viewModel.moments[index].createTime ?? DateTime.now()),
                numberOfComments: viewModel.moments[index].numberOfComments,
                numberOfLikes: viewModel.moments[index].numberOfLikes,
              );
            } else if (viewModel.hasMore) {
              return const Center(child: CircularProgressIndicator());
            } else {
              return const SizedBox.shrink();
            }
          },
          childCount: viewModel.moments.length + (viewModel.hasMore ? 1 : 0),
        ),
      ),
    );
  }

  Widget _buildSpotsGrid(OtherProfilePageViewModel viewModel) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (BuildContext context, int index) {
            if (index < viewModel.spots.length) {
              return _SpotListItem(spot: viewModel.spots[index]);
            } else if (viewModel.hasMore) {
              return const Center(child: CircularProgressIndicator());
            } else if (viewModel.spots.isEmpty) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(Icons.place_outlined, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('暂无创建的钓点', style: TextStyle(color: Colors.grey)),
                    ],
                  ),
                ),
              );
            } else {
              return const SizedBox.shrink();
            }
          },
          childCount: viewModel.spots.isEmpty && !viewModel.hasMore
              ? 1 // Show empty state
              : viewModel.spots.length + (viewModel.hasMore ? 1 : 0),
        ),
      ),
    );
  }
}

class _BuildImageItem extends StatelessWidget {
  final MomentVo moment;

  const _BuildImageItem({required this.moment});

  @override
  Widget build(BuildContext context) {
    // Check if pictures is null or empty
    if (moment.pictures == null || moment.pictures!.isEmpty) {
      return const Image(image: AssetImage('assets/default_fish.jpg'));
    }

    final picture = moment.pictures!.first;
    if (picture == '') {
      return const Image(image: AssetImage('assets/default_fish.jpg'));
    } else {
      return CachedNetworkImage(
        imageUrl: '$picture?x-oss-process=image/resize,w_200',
        fit: BoxFit.cover,
        placeholder: (context, url) => const Center(
          child: CircularProgressIndicator(),
        ),
        errorWidget: (context, url, error) =>
            const Image(image: AssetImage('assets/default_fish.jpg')),
      );
    }
  }
}

class _ListItem extends StatelessWidget {
  const _ListItem({
    required this.thumbnail,
    required this.content,
    this.tags,
    required this.location,
    required this.publishDate,
    required this.numberOfComments,
    required this.numberOfLikes,
  });

  final Widget thumbnail;
  final String content;
  final String? tags;
  final String location;
  final String publishDate;
  final num numberOfComments;
  final int numberOfLikes;

  @override
  Widget build(BuildContext context) {
    List<String> tagList = [];
    if (tags != null) {
      tagList = tags!.split(',');
      tagList.removeWhere((element) => element.isEmpty);
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          children: [
            SizedBox(
              height: 100,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: AspectRatio(
                      aspectRatio: 1.0,
                      child: thumbnail,
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20.0, 0.0, 2.0, 0.0),
                      child: _PostDescription(
                        content: content,
                        author: location,
                        publishDate: publishDate,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (tagList.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      spacing: 8.0,
                      children: tagList.map(
                        (tag) {
                          return Chip(
                            label: Text(tag),
                          );
                        },
                      ).toList(),
                    ),
                  ],
                ),
              ),
            const Divider(
              height: 1,
              thickness: 1,
            ),
            SizedBox(
              height: 40,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  _IconWithText(
                    icon: Icons.thumb_up,
                    text: numberOfLikes.toString(),
                  ),
                  _IconWithText(
                    icon: Icons.comment,
                    text: numberOfComments.toString(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PostDescription extends StatelessWidget {
  const _PostDescription({
    required this.content,
    required this.author,
    required this.publishDate,
  });

  final String content;
  final String author;
  final String publishDate;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Expanded(
          child: Text(
            content,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const Padding(padding: EdgeInsets.only(bottom: 2.0)),
        Text(
          author,
          style: const TextStyle(
            fontSize: 12.0,
            color: Colors.black87,
          ),
        ),
        Text(
          publishDate,
          style: const TextStyle(
            fontSize: 12.0,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }
}

class _IconWithText extends StatelessWidget {
  const _IconWithText({
    required this.icon,
    required this.text,
  });

  final IconData icon;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Icon(icon, size: 16.0),
        const Padding(padding: EdgeInsets.only(right: 4.0)),
        Text(text),
      ],
    );
  }
}

class _SpotListItem extends StatelessWidget {
  final FishingSpotVo spot;

  const _SpotListItem({required this.spot});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          // 导航到钓点详情页面
          context.push('${AppRoutes.fishingSpotDetail}?spotId=${spot.id}', extra: {
            'spotId': spot.id,
          });
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      spot.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (spot.isOfficial)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '官方',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.location_on, size: 14, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      spot.address,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      _IconWithText(
                        icon: Icons.remove_red_eye,
                        text: spot.visitorCount.toString(),
                      ),
                      const SizedBox(width: 16),
                      _IconWithText(
                        icon: Icons.star,
                        text: spot.rating.toStringAsFixed(1),
                      ),
                    ],
                  ),
                  if (!spot.isPaid)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '免费',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  else
                    Text(
                      '¥${spot.price?.toStringAsFixed(0) ?? "N/A"}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange.shade700,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
