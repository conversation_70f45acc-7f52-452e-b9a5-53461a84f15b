import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class ProfileSettingPage extends StatelessWidget {
  const ProfileSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Padding(
        padding: const EdgeInsets.only(left: 32.0, right: 32.0),
        child: Column(
          children: [
            ListView(
              shrinkWrap: true,
              children: [
                ElevatedButton(
                  onPressed: () {
                    context.read<AuthViewModel>().logout();
                    context.pop();
                  },
                  child: const Text('退出登录'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
