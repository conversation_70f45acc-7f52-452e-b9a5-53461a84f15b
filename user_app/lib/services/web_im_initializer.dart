import 'package:flutter/foundation.dart';
import 'dart:html' as html;
import 'dart:js' as js;
import 'dart:async';

class WebImInitializer {
  static bool _isSDKReady = false;
  static dynamic _timInstance;
  
  /// 检查Web环境中腾讯云IM SDK是否可用
  static bool isSDKAvailable() {
    if (!kIsWeb) return false;
    
    try {
      // 检查全局变量是否存在
      final timGlobal = js.context['TIM'];
      final timUploadPlugin = js.context['TIMUploadPlugin'];
      
      debugPrint('🔍 [WebIM] TIM SDK检查:');
      debugPrint('🔍 [WebIM] - TIM: ${timGlobal != null}');
      debugPrint('🔍 [WebIM] - TIMUploadPlugin: ${timUploadPlugin != null}');
      
      return timGlobal != null;
    } catch (e) {
      debugPrint('❌ [WebIM] SDK可用性检查失败: $e');
      return false;
    }
  }
  
  /// 初始化Web平台的TIM实例
  static Future<dynamic> initWebTIM({required int sdkAppID}) async {
    if (!kIsWeb) {
      throw Exception('此方法仅适用于Web平台');
    }
    
    if (!isSDKAvailable()) {
      throw Exception('Web平台TIM SDK未正确加载');
    }
    
    try {
      debugPrint('🚀 [WebIM] 开始初始化Web TIM...');
      
      // 获取TIM构造函数
      final timConstructor = js.context['TIM'];
      
      // 创建TIM实例 - Web平台使用TIM.create()
      final options = js.JsObject.jsify({
        'SDKAppID': sdkAppID,
      });
      
      _timInstance = timConstructor.callMethod('create', [options]);
      
      if (_timInstance == null) {
        throw Exception('Web TIM实例创建失败');
      }
      
      debugPrint('✅ [WebIM] Web TIM实例创建成功');
      
      // 设置日志级别
      _timInstance.callMethod('setLogLevel', [0]); // 0为debug级别
      
      // 注册插件
      final uploadPlugin = js.context['TIMUploadPlugin'];
      if (uploadPlugin != null) {
        _timInstance.callMethod('registerPlugin', [js.JsObject.jsify({
          'tim-upload-plugin': uploadPlugin,
        })]);
        debugPrint('✅ [WebIM] 上传插件注册成功');
      }
      
      _isSDKReady = true;
      debugPrint('✅ [WebIM] Web平台IM初始化完成');
      
      return _timInstance;
      
    } catch (e) {
      debugPrint('❌ [WebIM] Web TIM初始化失败: $e');
      rethrow;
    }
  }
  
  /// 获取当前TIM实例
  static dynamic getTIMInstance() {
    return _timInstance;
  }
  
  /// 检查SDK是否已准备就绪
  static bool isReady() {
    return _isSDKReady && _timInstance != null;
  }
  
  /// 登录Web TIM并等待SDK_READY
  static Future<bool> loginWeb({
    required String userID, 
    required String userSig
  }) async {
    if (!isReady()) {
      throw Exception('Web TIM未初始化');
    }
    
    try {
      debugPrint('🔐 [WebIM] 开始登录 userID: $userID');
      
      // 设置SDK_READY监听器
      final readyCompleter = Completer<bool>();
      
      final onReady = js.allowInterop((event) {
        debugPrint('✅ [WebIM] SDK_READY事件触发');
        if (!readyCompleter.isCompleted) {
          readyCompleter.complete(true);
        }
      });
      
      _timInstance.callMethod('on', [js.context['TIM']['EVENT']['SDK_READY'], onReady]);
      
      final loginPromise = _timInstance.callMethod('login', [js.JsObject.jsify({
        'userID': userID,
        'userSig': userSig,
      })]);
      
      // 等待登录Promise完成
      final loginResult = await _promiseToFuture(loginPromise);
      debugPrint('✅ [WebIM] 登录成功: $loginResult');
      
      // 等待SDK_READY事件（最多等待5秒）
      debugPrint('⏳ [WebIM] 等待SDK_READY事件...');
      final sdkReady = await Future.any([
        readyCompleter.future,
        Future.delayed(Duration(seconds: 5), () => false),
      ]);
      
      if (sdkReady) {
        debugPrint('✅ [WebIM] SDK已准备就绪');
        return true;
      } else {
        debugPrint('⚠️ [WebIM] SDK_READY事件超时，但继续执行');
        return true; // 即使超时也返回true，因为登录已成功
      }
      
    } catch (e) {
      debugPrint('❌ [WebIM] 登录失败: $e');
      return false;
    }
  }
  
  /// 将JS Promise转换为Dart Future
  static Future<dynamic> _promiseToFuture(dynamic jsPromise) {
    final completer = Completer<dynamic>();
    
    final onResolve = js.allowInterop((result) {
      completer.complete(result);
    });
    
    final onReject = js.allowInterop((error) {
      completer.completeError(error);
    });
    
    jsPromise.callMethod('then', [onResolve]).callMethod('catch', [onReject]);
    
    return completer.future;
  }
}