import 'package:flutter/cupertino.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/services/app_service.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class FollowButtonViewModel extends BaseViewModel {
  final num userId;
  final UserService _userService;
  final AuthViewModel _authViewModel;
  bool _isFollowing = false;
  bool _isLoggedIn = false;

  FollowButtonViewModel(
      this.userId, AppServices appServices, this._authViewModel)
      : _userService = appServices.userService;

  bool get isFollowing => _isFollowing;

  bool get isLoggedIn => _isLoggedIn;

  Future<void> init() async {
    _isLoggedIn = _authViewModel.isUserLoggedIn();
    if (_isLoggedIn) {
      setBusy(true);
      _isFollowing = await _userService.isFollowingUser(userId);
      setBusy(false);
    }
  }

  Future<void> toggleFollow(BuildContext context) async {
    if (!_isLoggedIn) {
      _authViewModel.checkAndPromptLogin(context);
      return;
    }

    setBusy(true);
    if (_isFollowing) {
      await _userService.unfollowUser(userId);
    } else {
      await _userService.followUser(userId);
    }
    _isFollowing = !_isFollowing;
    setBusy(false);
  }
}
