import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/moment/coordinate.dart';
import 'package:user_app/models/moment/create_moment_model.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/moment/moment_response.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/moment/query_comment_most_dto.dart';

import 'package:user_app/models/moment/user_moment_status_dto.dart';

class MomentApi extends BaseApi {
  static const String momentPath = 'moments';

  MomentApi(super.dio);

  Future<void> createMoment(CreateMomentModel createMomentModel) async {
    await safeApiCall<void>(
      () => dio.post(momentPath, data: createMomentModel.toJson()),
    );
  }

  Future<MomentResponse> getMoments(MomentListRequest request) async {
    return await safeApiCall<MomentResponse>(
      () => dio.post('$momentPath/list', data: request.toJson()),
      (data) => MomentResponse.fromMap(data),
    );
  }

  Future<MomentVo> getById(int momentId) async {
    return await safeApiCall<MomentVo>(
      () => dio.get('$momentPath/$momentId'),
      (data) => MomentVo.fromMap(data),
    );
  }

  Future<void> likeMoment(int momentId, bool isLike) async {
    await safeApiCall<void>(
      () => dio.post('$momentPath/$momentId/like', queryParameters: {
        'isLike': isLike,
      }),
    );
  }

  Future<void> incrementViewCount(int momentId) async {
    await safeApiCall<void>(
      () => dio.post('$momentPath/$momentId/view'),
    );
  }

  Future<UserMomentStatusDto> getUserMomentStatus(int momentId) async {
    return await safeApiCall<UserMomentStatusDto>(
      () => dio.get('$momentPath/$momentId/status'),
      (data) => UserMomentStatusDto.fromMap(data),
    );
  }

  Future<MomentResponse> fetchHotMoments(MomentListRequest request) async {
    return await safeApiCall<MomentResponse>(
      () => dio.post('$momentPath/hot', data: request.toJson()),
      (data) => MomentResponse.fromMap(data),
    );
  }

  Future<MomentResponse> fetchMostCommentedMoments(
      QueryCommentMostDto request) async {
    return await safeApiCall<MomentResponse>(
      () => dio.post('$momentPath/comment-most', data: request.toJson()),
      (data) => MomentResponse.fromMap(data),
    );
  }

  Future<List<Coordinate>> coordinates(MomentListRequest request) async {
    return await safeApiCall<List<Coordinate>>(
      () => dio.post('$momentPath/coordinates', data: request.toJson()),
      (data) => (data as List).map((e) => Coordinate.fromMap(e)).toList(),
    );
  }

  Future<MomentResponse> getMomentsByFishingSpot(
    int fishingSpotId, {
    int page = 1,
    int size = 20,
    String? momentType,
    String? sortBy,
  }) async {
    return await safeApiCall<MomentResponse>(
      () => dio.get('$momentPath/spot/$fishingSpotId', queryParameters: {
        'page': page,
        'size': size,
        if (momentType != null) 'momentType': momentType,
        if (sortBy != null) 'sortBy': sortBy,
      }),
      (data) => MomentResponse.fromMap(data),
    );
  }

  Future<MomentResponse> getFollowingMoments(int page, int size) async {
    return await safeApiCall<MomentResponse>(
      () => dio.post('$momentPath/following', data: {
        'page': page,
        'size': size,
      }),
      (data) => MomentResponse.fromMap(data),
    );
  }
}
