import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/notification/notification_vo.dart';

class NotificationApi extends BaseApi {
  static const String notificationPath = '/notifications';

  NotificationApi(super.dio);

  /// Get user notifications
  Future<List<NotificationVo>> getNotifications({
    int page = 1,
    int size = 20,
    bool? unreadOnly,
  }) async {
    return await safeApiCall<List<NotificationVo>>(
      () => dio.get(notificationPath, queryParameters: {
        'page': page,
        'size': size,
        if (unreadOnly != null) 'unreadOnly': unreadOnly,
      }),
      (data) {
        if (data is List) {
          return data.map((item) => NotificationVo.fromMap(item)).toList();
        }
        return <NotificationVo>[];
      },
    );
  }

  /// Mark notification as read
  Future<void> markAsRead(int notificationId) async {
    await safeApiCall<void>(
      () => dio.put('$notificationPath/$notificationId/read'),
    );
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await safeApiCall<void>(
      () => dio.put('$notificationPath/read-all'),
    );
  }

  /// Get unread notification count
  Future<int> getUnreadCount() async {
    return await safeApiCall<int>(
      () => dio.get('$notificationPath/unread-count'),
      (data) => data['count'] as int? ?? 0,
    );
  }

  /// Delete notification
  Future<void> deleteNotification(int notificationId) async {
    await safeApiCall<void>(
      () => dio.delete('$notificationPath/$notificationId'),
    );
  }
}
