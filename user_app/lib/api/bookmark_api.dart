import 'package:user_app/core/network/base_api.dart';

class BookmarkApi extends BaseApi {
  static const String bookmarkPath = '/bookmarks';

  BookmarkApi(super.dio);

  /// Bookmark a moment
  Future<void> bookmarkMoment(int momentId) async {
    await safeApiCall<void>(
      () => dio.post('$bookmarkPath/moment/$momentId'),
    );
  }

  /// Remove bookmark from a moment
  Future<void> unbookmarkMoment(int momentId) async {
    await safeApiCall<void>(
      () => dio.delete('$bookmarkPath/moment/$momentId'),
    );
  }

  /// Check if a moment is bookmarked
  Future<bool> isBookmarked(int momentId) async {
    return await safeApiCall<bool>(
      () => dio.get('$bookmarkPath/moment/$momentId/status'),
      (data) => data['bookmarked'] as bool? ?? false,
    );
  }
}
