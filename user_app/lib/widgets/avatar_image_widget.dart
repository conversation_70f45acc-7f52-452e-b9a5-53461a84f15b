import 'package:flutter/material.dart';

class AvatarImage extends StatelessWidget {
  final String? avatarUrl;
  final double size;

  const AvatarImage(this.avatarUrl, {super.key, this.size = 40});

  @override
  Widget build(BuildContext context) {
    return ClipOval(
      child: SizedBox(
        width: size,
        height: size,
        child: avatarUrl?.isNotEmpty == true
            ? Image.network(
                avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (_, __, ___) => _buildPlaceholder(),
                loadingBuilder: (_, child, loadingProgress) =>
                    loadingProgress == null ? child : _buildPlaceholder(),
              )
            : _buildPlaceholder(),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[300],
      child: Icon(
        Icons.person,
        size: size * 0.6,
        color: Colors.grey[600],
      ),
    );
  }
}
