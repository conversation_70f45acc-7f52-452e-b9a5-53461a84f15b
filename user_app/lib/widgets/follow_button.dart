import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/view_models/follow_button_view_model.dart';

class FollowButton extends StatelessWidget {
  final num userId;

  const FollowButton({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<FollowButtonViewModel>(
      create: (context) => getIt<FollowButtonViewModel>(param1: userId)..init(),
      child: Consumer<FollowButtonViewModel>(
        builder: (context, viewModel, child) {
          return ElevatedButton(
            onPressed: () => viewModel.toggleFollow(context),
            child: Text(viewModel.isFollowing ? '取消关注' : '关注'),
          );
        },
      ),
    );
  }
}
