import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/config/app_routes.dart';

class LoggedOutUserWidget extends StatelessWidget {
  const LoggedOutUserWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const CircleAvatar(
          radius: 50,
          backgroundImage: AssetImage('assets/default_avatar.png'),
        ),
        const SizedBox(height: 16),
        const Text(
          '您当前为游客状态',
          style: TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 8),
        const Text(
          '登录后可使用更多功能',
          style: TextStyle(fontSize: 14, color: Colors.grey),
        ),
        const SizedBox(height: 24),
        Row(
          children: [
            Expanded(
              child: FractionallySizedBox(
                widthFactor: 2 / 3, // 按钮宽度占父容器宽度的2/3
                child: FilledButton.icon(
                  onPressed: () {
                    context.push(AppRoutes.login);
                  },
                  icon: const Icon(Icons.login),
                  label: const Text('登录'),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
