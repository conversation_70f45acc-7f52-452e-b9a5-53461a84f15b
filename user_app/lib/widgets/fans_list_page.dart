import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/view_models/fans_list_view_model.dart';
import 'package:user_app/widgets/user_circle_avatar.dart';

class FansListPage extends StatelessWidget {
  final num userId;

  const FansListPage({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => FansListViewModel(
        userId: userId,
        appServices: context.read(),
      ),
      child: _FansListPage(),
    );
  }
}

class _FansListPage extends StatefulWidget {
  @override
  State<_FansListPage> createState() => _FansListPageState();
}

class _FansListPageState extends State<_FansListPage> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_loadMoreFans);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await context.read<FansListViewModel>().fetchFans(reset: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('粉丝列表'),
      ),
      body: Selector<FansListViewModel, List<User>>(
        selector: (context, viewModel) => viewModel.users,
        builder: (context, users, child) {
          return ListView.separated(
            separatorBuilder: (context, index) => const Divider(),
            itemCount: users.length,
            itemBuilder: (context, index) {
              return ListTile(
                leading: UserCircleAvatar(
                  avatarUrl: users[index].avatarUrl,
                ),
                title: Text(users[index].name ?? ''),
                subtitle: Text('粉丝数：${users[index].followCount}'),
              );
            },
          );
        },
      ),
    );
  }

  void _loadMoreFans() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      context.read<FansListViewModel>().fetchFans();
    }
  }
}
