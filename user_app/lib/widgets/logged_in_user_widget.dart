import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotlottie_loader/dotlottie_loader.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:user_app/screens/mine/profile_setting_page.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class LoggedInUserWidget extends StatefulWidget {
  const LoggedInUserWidget({super.key});

  @override
  State<LoggedInUserWidget> createState() => _LoggedInUserWidgetState();
}

class _LoggedInUserWidgetState extends State<LoggedInUserWidget> {
  late final AuthViewModel authViewModel;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      authViewModel = context.read<AuthViewModel>();
      await authViewModel.getCurrentUser();

      if (!mounted) return;
      await authViewModel.fetchStatistics(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  Positioned(
                    child: DotLottieLoader.fromAsset(
                      "assets/lottie/fish3.json",
                      frameBuilder: (ctx, dotLottie) {
                        if (dotLottie != null) {
                          return Lottie.memory(
                              dotLottie.animations.values.single);
                        } else {
                          return Container();
                        }
                      },
                    ),
                  ),
                  Positioned(
                    top: 30,
                    child: Selector<AuthViewModel, String?>(
                      selector: (context, viewModel) =>
                          viewModel.currentUser?.avatarUrl,
                      builder: (context, avatarUrl, child) {
                        if (avatarUrl == null || avatarUrl == "") {
                          return const CircleAvatar(
                            radius: 50,
                            backgroundImage:
                                AssetImage('assets/default_avatar.png'),
                          );
                        }
                        return CircleAvatar(
                          radius: 40,
                          backgroundImage: CachedNetworkImageProvider(
                            '$avatarUrl?x-oss-process=image/resize,m_lfit,w_200/crop,g_center/quality,Q_100',
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
              ListView(
                shrinkWrap: true,
                children: [
                  const _ProfileInfoRow(),
                  const SizedBox(height: 20),
                  Card(
                    child: ListTile(
                      title: const Text('设置'),
                      leading: const Icon(Icons.settings),
                      trailing: const Icon(Icons.more_vert),
                      onTap: () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const ProfileSettingPage(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ProfileInfoRow extends StatefulWidget {
  const _ProfileInfoRow();

  @override
  State<_ProfileInfoRow> createState() => _ProfileInfoRowState();
}

class _ProfileInfoRowState extends State<_ProfileInfoRow> {
  @override
  Widget build(BuildContext context) {
    return Selector<AuthViewModel, List<ProfileInfoItem>>(
      selector: (context, viewModel) => viewModel.profileInfoItems,
      builder: (context, profileInfoItems, child) {
        return Container(
          height: 80,
          constraints: const BoxConstraints(maxWidth: 400),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: profileInfoItems
                .map(
                  (item) => Expanded(
                    child: Row(
                      children: [
                        if (profileInfoItems.indexOf(item) != 0)
                          const VerticalDivider(),
                        Expanded(
                          child: _singleItem(context, item),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _singleItem(BuildContext context, ProfileInfoItem item) => InkWell(
        onTap: () {
          if (item.onTap != null) {
            item.onTap!();
          }
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                item.value.toString(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
            ),
            Text(
              item.title,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      );
}
