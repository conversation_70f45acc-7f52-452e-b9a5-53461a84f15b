import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/view_models/attentions_list_view_model.dart';
import 'package:user_app/widgets/user_circle_avatar.dart';

class AttentionsListPage extends StatelessWidget {
  final num userId;

  const AttentionsListPage({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AttentionsListViewModel(
        userId: userId,
        appServices: context.read(),
      ),
      child: _AttentionsListPage(),
    );
  }
}

class _AttentionsListPage extends StatefulWidget {
  @override
  State<_AttentionsListPage> createState() => _AttentionsListPageState();
}

class _AttentionsListPageState extends State<_AttentionsListPage> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await context
          .read<AttentionsListViewModel>()
          .fetchAttentions(reset: true);
    });
  }

  void _scrollListener() {
    if (context.read<AttentionsListViewModel>().busy) return;

    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    final viewModel = context.read<AttentionsListViewModel>();
    if (!viewModel.busy && viewModel.hasMore) {
      await viewModel.fetchAttentions();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('关注列表'),
      ),
      body: Consumer<AttentionsListViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.busy && viewModel.users.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          return RefreshIndicator(
            onRefresh: () => viewModel.fetchAttentions(reset: true),
            child: ListView.separated(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              separatorBuilder: (context, index) => const Divider(),
              itemCount: viewModel.users.length + 1,
              itemBuilder: (context, index) {
                if (index == viewModel.users.length) {
                  return _buildLoadingIndicator(viewModel);
                }

                final user = viewModel.users[index];
                return ListTile(
                  leading: UserCircleAvatar(
                    avatarUrl: user.avatarUrl,
                  ),
                  title: Text(user.name ?? ''),
                  subtitle: Text('粉丝数：${user.followCount}'),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingIndicator(AttentionsListViewModel viewModel) {
    if (!viewModel.hasMore) {
      return const SizedBox(
        height: 60,
        child: Center(child: Text('没有更多数据了')),
      );
    }

    if (viewModel.busy) {
      return const SizedBox(
        height: 60,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return const SizedBox(height: 60);
  }
}
