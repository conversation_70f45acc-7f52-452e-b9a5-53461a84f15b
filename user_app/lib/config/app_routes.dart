class AppRoutes {
  // Main navigation tabs
  static const String fishingSpots = '/fishing_spots';
  static const String publishMoment = '/publish_moment';

  static const String community = '/community';
  static const String tips = '/tips';
  static const String message = '/message';
  static const String mine = '/mine';

  // Other routes
  static const String search = '/search';
  static const String login = '/login';
  static const String register = '/register';
  static const String resetPassword = '/resetPassword';
  static const String profile = '/other_profile_page';
  static const String fansPage = '/fansPage/:userId';
  static const String attentionsPage = '/attentionsPage/:userId';

  ///预览图片
  static const String previewPictures = '/previewPictures';

  static const String momentDetail = '/momentDetail';
  static const String fishingSpotDetail = '/fishingSpotDetail';

  ///个人动态列表
  static const String personalMomentList = '/personalMomentList';
  
  // Chat routes
  static const String chatList = '/chat_list';
  static const String chatDetail = '/chat/simple/:conversationID';
  static const String followedUsers = '/followed_users';
  
  // User routes
  static const String userProfile = '/user_profile';
}
