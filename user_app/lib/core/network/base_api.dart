import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:user_app/core/network/api_error.dart';

abstract class BaseApi {
  final Dio dio;

  BaseApi(this.dio);

  /// 统一的API调用方法，专门处理ApiResponse格式
  ///
  /// [apiCall] - 实际的API调用函数
  /// [dataConverter] - 可选的数据转换函数，用于将data字段转换为特定类型
  ///
  /// 返回转换后的数据，如果失败则抛出ApiError异常
  Future<T> safeApiCall<T>(
    Future<Response> Function() apiCall, [
    T Function(dynamic)? dataConverter,
  ]) async {
    try {
      final response = await apiCall();

      // 统一处理ApiResponse格式
      if (response.data is Map<String, dynamic>) {
        final apiResponse = response.data as Map<String, dynamic>;

        // 检查是否是标准的ApiResponse格式
        if (apiResponse.containsKey('code') &&
            apiResponse.containsKey('message')) {
          final code = apiResponse['code'] as int;
          final message = apiResponse['message'] as String;

          if (code == 200) {
            // 成功响应，提取并转换data
            final data = apiResponse['data'];
            if (dataConverter != null) {
              return dataConverter(data);
            } else {
              return data as T;
            }
          } else {
            // 业务错误，抛出ApiError
            throw ApiError(code: code, message: message);
          }
        } else {
          // 不是标准ApiResponse格式，直接返回数据（向后兼容）
          debugPrint('⚠️ 检测到非标准ApiResponse格式，建议后端统一响应格式');
          if (dataConverter != null) {
            return dataConverter(response.data);
          } else {
            return response.data as T;
          }
        }
      } else {
        // 非Map格式的响应
        if (dataConverter != null) {
          return dataConverter(response.data);
        } else {
          return response.data as T;
        }
      }
    } on DioException catch (e) {
      debugPrint("Dio异常: ${e.message}");
      if (e.response != null) {
        debugPrint("Dio异常响应: ${e.response?.data}");
      }

      // 尝试从错误响应中提取ApiResponse格式的错误信息
      final errorData = e.response?.data;
      if (errorData is Map<String, dynamic> &&
          errorData.containsKey('code') &&
          errorData.containsKey('message')) {
        throw ApiError(
          code: errorData['code'] as int,
          message: errorData['message'] as String,
        );
      }

      // 使用HTTP状态码和默认错误信息
      throw ApiError(
        code: e.response?.statusCode ?? 500,
        message: e.response?.data?['message'] ?? e.message ?? 'Network error',
      );
    } catch (e) {
      debugPrint("API调用异常: $e");

      // 如果是ApiError，直接重新抛出
      if (e is ApiError) {
        rethrow;
      }

      // 其他异常包装为ApiError
      throw ApiError(
        code: 500,
        message: 'Unexpected error: ${e.toString()}',
      );
    }
  }
}
