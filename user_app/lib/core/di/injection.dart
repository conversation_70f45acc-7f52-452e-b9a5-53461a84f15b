import 'package:algoliasearch/algoliasearch_lite.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/api/bookmark_api.dart';
import 'package:user_app/api/chat_api.dart';
import 'package:user_app/api/comment_api.dart';
import 'package:user_app/api/fish_type_api.dart';
import 'package:user_app/api/fishing_recommend_api.dart';
import 'package:user_app/api/fishing_spot_api.dart';
import 'package:user_app/api/map_api.dart';
import 'package:user_app/api/moment_api.dart';
import 'package:user_app/api/oss_api.dart';
import 'package:user_app/api/report_api.dart';
import 'package:user_app/api/search_api.dart';
import 'package:user_app/api/session_api.dart';
import 'package:user_app/api/sms_api.dart';
import 'package:user_app/api/statistics_api.dart';
import 'package:user_app/api/user_api.dart';
import 'package:user_app/api/user_follow_api.dart';
import 'package:user_app/config/global_config.dart';
import 'package:user_app/features/auth/providers/base_auth_view_model.dart';
import 'package:user_app/features/auth/providers/login_view_model.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';
import 'package:user_app/features/auth/providers/user_profile_view_model.dart';
import 'package:user_app/features/auth/services/verification_code_service.dart';
import 'package:user_app/features/community/view_models/comment_view_model.dart';
import 'package:user_app/features/community/view_models/community_view_model.dart';
import 'package:user_app/features/fishing_spots/services/fish_type_service.dart';
import 'package:user_app/features/fishing_spots/services/fishing_recommend_service.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_search_service.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/view_models/create_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/location_selection_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/map_state_provider.dart';
import 'package:user_app/features/fishing_spots/view_models/moment_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/search_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/features/tips/view_models/tips_view_model.dart';
import 'package:user_app/services/app_service.dart';
import 'package:user_app/services/chat_service.dart';
import 'package:user_app/services/comment_service.dart';
import 'package:user_app/services/map_service.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/services/oss_service.dart';
import 'package:user_app/services/search_service.dart';
import 'package:user_app/services/session_service.dart';
import 'package:user_app/services/sms_service.dart';
import 'package:user_app/services/statistics_service.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/view_models/chat_view_model.dart';
import 'package:user_app/view_models/follow_button_view_model.dart';
import 'package:user_app/view_models/other_profile_page_view_model.dart';
import 'package:user_app/view_models/preview_image_view_model.dart';

import '../../features/fishing_spots/view_models/publish_moment_view_model.dart';

final GetIt getIt = GetIt.instance;

class ServiceRegistration<T extends Object> {
  final T Function(GetIt) factory;

  const ServiceRegistration(this.factory);
}

class _DIRegistration {
  static void _registerApis(GetIt i) {
    i.registerLazySingleton<ChatApi>(() => ChatApi(i<Dio>()));
    i.registerLazySingleton<CommentApi>(() => CommentApi(i<Dio>()));
    i.registerLazySingleton<MapApi>(() => MapApi(i<Dio>()));
    i.registerLazySingleton<MomentApi>(() => MomentApi(i<Dio>()));
    i.registerLazySingleton<OssApi>(() => OssApi(i<Dio>()));
    i.registerLazySingleton<SessionApi>(() => SessionApi(i<Dio>()));
    i.registerLazySingleton<SmsApi>(() => SmsApi(i<Dio>()));
    i.registerLazySingleton<StatisticsApi>(() => StatisticsApi(i<Dio>()));
    i.registerLazySingleton<UserApi>(() => UserApi(i<Dio>()));
    i.registerLazySingleton<UserFollowApi>(() => UserFollowApi(i<Dio>()));
    i.registerLazySingleton<FishingSpotApi>(() => FishingSpotApi(i<Dio>()));
    i.registerLazySingleton<FishTypeApi>(() => FishTypeApi(i<Dio>()));
    i.registerLazySingleton<FishingRecommendApi>(
        () => FishingRecommendApi(i<Dio>()));
    i.registerLazySingleton<SearchApi>(() => SearchApi(i<Dio>()));
    i.registerLazySingleton<BookmarkApi>(() => BookmarkApi(i<Dio>()));
    i.registerLazySingleton<ReportApi>(() => ReportApi(i<Dio>()));
  }

  static void _registerServices(GetIt i) {
    i.registerLazySingleton<ChatService>(() => ChatService(i<ChatApi>()));
    i.registerLazySingleton<CommentService>(
        () => CommentService(i<CommentApi>()));
    i.registerLazySingleton<MapService>(() => MapService(i<MapApi>()));
    i.registerLazySingleton<MomentService>(() => MomentService(i<MomentApi>()));
    i.registerLazySingleton<OssService>(() => OssService(ossApi: i<OssApi>()));
    i.registerLazySingleton<SearchService>(() => SearchService(i<SearchApi>()));
    i.registerLazySingleton<SessionService>(
        () => SessionService(i<SessionApi>()));
    i.registerLazySingleton<SmsService>(() => SmsService(i<SmsApi>()));
    i.registerLazySingleton<StatisticsService>(
        () => StatisticsService(i<StatisticsApi>()));
    i.registerLazySingleton<UserService>(() =>
        UserService(i<UserApi>(), i<UserFollowApi>(), i<SharedPreferences>()));
    i.registerLazySingleton<FishingSpotService>(
        () => FishingSpotService(i<FishingSpotApi>(), i<SearchService>()));
    i.registerLazySingleton<FishTypeService>(
        () => FishTypeService(i<FishTypeApi>()));
    i.registerLazySingleton<FishingRecommendService>(
        () => FishingRecommendService(i<FishingRecommendApi>()));
    i.registerLazySingleton<FishingSpotSearchService>(
        () => FishingSpotSearchService(i<SearchClient>()));
    i.registerLazySingleton<AppServices>(() => AppServices(
          userService: i<UserService>(),
          ossService: i<OssService>(),
          commentService: i<CommentService>(),
          momentService: i<MomentService>(),
          sessionService: i<SessionService>(),
          smsService: i<SmsService>(),
          statisticsService: i<StatisticsService>(),
          searchService: i<SearchService>(),
          chatService: i<ChatService>(),
          mapService: i<MapService>(),
          fishingSpotService: i<FishingSpotService>(),
        ));
  }

  static void _registerViewModels(GetIt i) {
    _registerAuthViewModels(i);

    // Register providers
    i.registerLazySingleton<MomentViewModel>(
        () => MomentViewModel(i<MomentService>()));

    i.registerLazySingleton<AuthViewModel>(
      () => AuthViewModel(
        appServices: i<AppServices>(),
        sharedPreferences: i<SharedPreferences>(),
      ),
    );
    i.registerFactory<ChatViewModel>(
      () => ChatViewModel(appServices: i<AppServices>()),
    );
    i.registerFactoryParam<OtherProfilePageViewModel, int, void>(
      (userId, _) => OtherProfilePageViewModel(
        userId: userId,
        appServices: i<AppServices>(),
      ),
    );
    i.registerFactory<PreviewImageViewModel>(
      () => PreviewImageViewModel(),
    );
    i.registerFactoryParam<FollowButtonViewModel, int, void>(
      (userId, _) =>
          FollowButtonViewModel(userId, i<AppServices>(), i<AuthViewModel>()),
    );
    i.registerFactory<FishingSpotViewModel>(
      () => FishingSpotViewModel(
        fishSpotService: i<FishingSpotService>(),
        fishTypeService: i<FishTypeService>(),
      ),
    );
    i.registerFactory<WeatherCardViewModel>(
      () => WeatherCardViewModel(
        mapService: i<MapService>(),
        fishingRecommendService: i<FishingRecommendService>(),
      ),
    );
    i.registerFactory<PublishMomentViewModel>(
      () => PublishMomentViewModel(
        momentViewModel: i<MomentViewModel>(),
        ossService: i<OssService>(),
      ),
    );
    i.registerFactory<CommunityViewModel>(
      () => CommunityViewModel(
        i<MomentService>(),
        i<SearchService>(),
        i<UserFollowApi>(),
        i<BookmarkApi>(),
        i<ReportApi>(),
      ),
    );
    i.registerFactory<TipsViewModel>(
      () => TipsViewModel(
        i<MomentService>(),
      ),
    );
    i.registerFactory<MapStateViewModel>(
      () => MapStateViewModel(),
    );
    i.registerFactory<CreateSpotViewModel>(
      () => CreateSpotViewModel(
        ossService: i<OssService>(),
        fishingSpotViewModel: i<FishingSpotViewModel>(),
        fishSpotService: i<FishingSpotService>(),
      ),
    );
    i.registerFactory<LocationSelectionViewModel>(
      () => LocationSelectionViewModel(
        fishingSpotService: i<FishingSpotService>(),
      ),
    );
    i.registerFactory<CommentViewModel>(
      () => CommentViewModel(i<CommentService>()),
    );
    i.registerFactory<SearchViewModel>(
      () => SearchViewModel(
        searchService: i<FishingSpotSearchService>(),
        prefs: i<SharedPreferences>(),
      ),
    );
  }

  static void _registerAuthViewModels(GetIt i) {
    // Register base auth view model
    i.registerLazySingleton<BaseAuthViewModel>(() => BaseAuthViewModel(
          userService: i<UserService>(),
          sharedPreferences: i<SharedPreferences>(),
        ));

    // Register verification code service
    i.registerLazySingleton<VerificationCodeService>(
        () => VerificationCodeService(
              smsService: i<SmsService>(),
              sharedPreferences: i<SharedPreferences>(),
            ));

    // Register login view model
    i.registerFactory<LoginViewModel>(() => LoginViewModel(
          baseAuthViewModel: i<BaseAuthViewModel>(),
          sessionService: i<SessionService>(),
        ));

    // Register register view model
    i.registerFactory<RegisterViewModel>(() => RegisterViewModel(
          baseAuthViewModel: i<BaseAuthViewModel>(),
          sessionService: i<SessionService>(),
          verificationCodeService: i<VerificationCodeService>(),
        ));

    // Register reset password view model
    i.registerFactory<ResetPasswordViewModel>(() => ResetPasswordViewModel(
          baseAuthViewModel: i<BaseAuthViewModel>(),
          sessionService: i<SessionService>(),
          verificationCodeService: i<VerificationCodeService>(),
        ));

    // Register user profile view model
    i.registerFactory<UserProfileViewModel>(() => UserProfileViewModel(
          baseAuthViewModel: i<BaseAuthViewModel>(),
          statisticsService: i<StatisticsService>(),
        ));
  }

  static Future<void> setupDependencies(GetIt i) async {
    // Register core dependencies
    await _registerCoreDependencies(i);

    // Register APIs
    _registerApis(i);

    // Register Services
    _registerServices(i);

    // Register ViewModels
    _registerViewModels(i);
  }

  static Future<void> _registerCoreDependencies(GetIt i) async {
    // Register SharedPreferences synchronously
    final sharedPreferences = await SharedPreferences.getInstance();
    i.registerLazySingleton<SharedPreferences>(() => sharedPreferences);

    i.registerLazySingleton<SearchClient>(
      () => SearchClient(
        appId: GlobalConfig.algoliaAppId,
        apiKey: GlobalConfig.algoliaApiKey,
      ),
    );

    // Register Dio
    i.registerLazySingleton<Dio>(() {
      final dio = Dio(BaseOptions(
        baseUrl: GlobalConfig.baseUrl,
        connectTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 60),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        responseType: ResponseType.json,
      ));

      dio.interceptors.add(InterceptorsWrapper(
        onRequest: (options, handler) async {
          debugPrint('=== HTTP Request ===');
          debugPrint('URL: ${options.baseUrl}${options.path}');
          debugPrint('Method: ${options.method}');
          debugPrint('Headers: ${options.headers}');
          debugPrint('Data: ${options.data}');

          // 移除GET请求的Content-Type
          if (options.method == 'GET' && options.headers.containsKey('Content-Type')) {
            options.headers.remove('Content-Type');
            debugPrint('🔧 Removed Content-Type from GET request');
          }

          try {
            final sharedPreferences = getIt<SharedPreferences>();
            final accessToken = sharedPreferences.getString('token');
            
            debugPrint('🔍 [HTTP拦截器] SharedPreferences实例: ${sharedPreferences.hashCode}');
            debugPrint('🔍 [HTTP拦截器] Token检查结果: ${accessToken != null ? "存在(${accessToken.length}字符)" : "不存在"}');

            if (accessToken != null) {
              options.headers['Authorization'] = 'Bearer $accessToken';
              debugPrint('✅ Authorization header added with token length: ${accessToken.length}');
            } else {
              debugPrint('⚠️ No token found in SharedPreferences');
            }
          } catch (e) {
            debugPrint('❌ Error getting token from SharedPreferences: $e');
          }

          return handler.next(options);
        },
        onResponse: (response, handler) {
          debugPrint('=== HTTP Response ===');
          debugPrint('Status Code: ${response.statusCode}');
          debugPrint('Response Data: ${response.data}');

          if (response.statusCode == 401) {
            final sharedPreferences = getIt<SharedPreferences>();
            sharedPreferences.remove('token');
          }
          return handler.next(response);
        },
        onError: (DioException err, handler) {
          debugPrint('=== HTTP Error ===');
          debugPrint('Error Type: ${err.type}');
          debugPrint('Error Message: ${err.message}');
          debugPrint('Status Code: ${err.response?.statusCode}');
          debugPrint('Response Data: ${err.response?.data}');

          if (err.error is FormatException) {
            return handler.resolve(Response(
              requestOptions: err.requestOptions,
              data: {
                'message': 'Format Error',
              },
            ));
          }

          if (err.response?.statusCode == 401) {
            final sharedPreferences = getIt<SharedPreferences>();
            sharedPreferences.remove('token');
          }
          return handler.next(err);
        },
      ));

      return dio;
    });
  }
}

Future<void> setupInjection() async {
  await _DIRegistration.setupDependencies(getIt);
}
