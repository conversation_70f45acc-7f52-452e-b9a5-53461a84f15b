import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/models/search/search_models.dart';
import 'package:user_app/services/algolia_search_service.dart';

class CommunitySearchPage extends StatefulWidget {
  const CommunitySearchPage({super.key});

  @override
  State<CommunitySearchPage> createState() => _CommunitySearchPageState();
}

class _CommunitySearchPageState extends State<CommunitySearchPage>
    with TickerProviderStateMixin {
  late AlgoliaSearchService _searchService;
  late TextEditingController _searchController;
  late AnimationController _filterAnimationController;
  late AnimationController _searchAnimationController;
  late FocusNode _searchFocusNode;

  List<SearchResultVO> _searchResults = [];
  List<SearchSuggestionVO> _suggestions = [];
  List<String> _searchHistory = [];
  List<String> _hotKeywords = [];

  bool _isLoading = false;
  bool _showSuggestions = false;
  bool _isSearching = false;
  bool _isFocused = false;
  String _selectedType = 'all';
  String _selectedSort = 'relevance';
  int _currentPage = 1;
  int _totalCount = 0;

  final List<Map<String, dynamic>> _searchTypes = [
    {'key': 'all', 'name': '全部', 'icon': Icons.apps_rounded},
    {
      'key': 'moment',
      'name': '动态',
      'icon': Icons.article_rounded,
      'subtypes': [
        {'key': 'fishing_catch', 'name': '钓获分享', 'icon': Icons.phishing},
        {'key': 'equipment', 'name': '装备展示', 'icon': Icons.construction},
        {'key': 'technique', 'name': '技巧分享', 'icon': Icons.lightbulb_outline},
        {'key': 'question', 'name': '问答求助', 'icon': Icons.help_outline},
      ]
    },
    {'key': 'user', 'name': '用户', 'icon': Icons.person_rounded},
    {'key': 'spot', 'name': '钓点', 'icon': Icons.location_on_rounded},
  ];

  final List<Map<String, String>> _sortTypes = [
    {'key': 'relevance', 'name': '相关性'},
    {'key': 'time', 'name': '时间'},
    {'key': 'hot', 'name': '热度'},
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchFocusNode = FocusNode();
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _searchFocusNode.addListener(() {
      setState(() {
        _isFocused = _searchFocusNode.hasFocus;
      });
    });

    _initSearchService();
    _loadInitialData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _filterAnimationController.dispose();
    _searchAnimationController.dispose();
    _searchService.dispose();
    super.dispose();
  }

  void _initSearchService() {
    _searchService = AlgoliaSearchService();
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadSearchHistory(),
      _loadHotKeywords(),
    ]);
  }

  Future<void> _loadSearchHistory() async {
    try {
      final history = await _searchService.getSearchHistory();
      setState(() {
        _searchHistory = history;
      });
    } catch (e) {
      debugPrint('加载搜索历史失败: $e');
    }
  }

  Future<void> _loadHotKeywords() async {
    try {
      final keywords = await _searchService.getHotKeywords();
      setState(() {
        _hotKeywords = keywords;
      });
    } catch (e) {
      debugPrint('加载热门搜索失败: $e');
    }
  }

  void _triggerSearch() {
    final keyword = _searchController.text.trim();
    if (keyword.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('请输入搜索关键词'),
          backgroundColor: Colors.orange.shade600,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
      return;
    }

    FocusScope.of(context).unfocus();
    _performSearch(keyword);
  }

  Future<void> _performSearch(String keyword, {bool reset = true}) async {
    if (keyword.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
      _showSuggestions = false;
      if (reset) {
        _searchResults = [];
        _currentPage = 1;
      }
      _isLoading = true;
    });

    _searchAnimationController.forward();

    try {
      final result = await _searchService.search(
        keyword,
        type: _selectedType,
        sortBy: _selectedSort,
        page: _currentPage,
        size: 20,
      );

      await _searchService.addToSearchHistory(keyword);
      await _loadSearchHistory();

      setState(() {
        if (reset) {
          _searchResults = result.records;
        } else {
          _searchResults.addAll(result.records);
        }
        _totalCount = result.total;
        _currentPage++;
        _isLoading = false;
      });

      if (_searchResults.isNotEmpty) {
        _filterAnimationController.forward();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        if (!reset) _currentPage--;
      });
      _showErrorSnackBar('搜索失败，请重试');
    }
  }

  Future<void> _loadMoreResults() async {
    if (_isLoading || _searchResults.length >= _totalCount) return;
    _performSearch(_searchController.text, reset: false);
  }

  Future<void> _updateSuggestions(String keyword) async {
    if (keyword.trim().isEmpty) {
      setState(() {
        _suggestions = [];
        _showSuggestions = false;
        _isSearching = false;
      });
      _searchAnimationController.reverse();
      _filterAnimationController.reverse();
      return;
    }

    if (keyword.isEmpty) return;

    setState(() {
      _showSuggestions = true;
    });

    try {
      final suggestions = await _searchService.getSearchSuggestions(keyword);
      setState(() {
        _suggestions = suggestions;
      });
    } catch (e) {
      debugPrint('获取搜索建议失败: $e');
    }
  }

  void _selectSuggestion(String suggestion) {
    _searchController.text = suggestion;
    _performSearch(suggestion);
  }

  void _selectHistoryKeyword(String keyword) {
    _searchController.text = keyword;
    _performSearch(keyword);
  }

  void _deleteHistoryKeyword(String keyword) async {
    await _searchService.removeFromSearchHistory(keyword);
    await _loadSearchHistory();
  }

  Future<void> _clearSearchHistory() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空搜索历史'),
        content: const Text('确定要清空所有搜索历史吗？'),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await _searchService.clearSearchHistory();
              if (success) {
                setState(() {
                  _searchHistory = [];
                });
              }
            },
            child: Text(
              '确定',
              style: TextStyle(color: Colors.red.shade600),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Column(
          children: [
            // 搜索栏
            Container(
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.arrow_back_rounded),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    const SizedBox(width: 12),
                    Expanded(child: _buildSearchBar()),
                  ],
                ),
              ),
            ),

            // 筛选栏
            AnimatedBuilder(
              animation: _filterAnimationController,
              builder: (context, child) {
                return SizeTransition(
                  sizeFactor: _filterAnimationController,
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    height: _isSearching && _searchResults.isNotEmpty ? 56 : 0,
                    child: _isSearching && _searchResults.isNotEmpty
                        ? _buildModernFilterBar()
                        : const SizedBox.shrink(),
                  ),
                );
              },
            ),

            // 主要内容区域
            Expanded(
              child: _showSuggestions
                  ? _buildModernSuggestionsView()
                  : _isSearching
                      ? _searchResults.isEmpty && _isLoading
                          ? _buildSkeletonLoader()
                          : _searchResults.isEmpty
                              ? _buildNoResultsState()
                              : _buildModernSearchResults()
                      : _buildModernEmptyState(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      height: 44,
      decoration: BoxDecoration(
        color: _isFocused ? Colors.white : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(22),
        border: Border.all(
          color: _isFocused
              ? Theme.of(context).primaryColor
              : Colors.grey.shade200,
          width: _isFocused ? 2 : 1,
        ),
        boxShadow: _isFocused
            ? [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Row(
        children: [
          // 搜索图标
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: AnimatedBuilder(
              animation: _searchAnimationController,
              builder: (context, child) {
                return _isLoading
                    ? SizedBox(
                        width: 22,
                        height: 22,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                        ),
                      )
                    : Transform.rotate(
                        angle: _searchAnimationController.value * 0.5,
                        child: Icon(
                          Icons.search_rounded,
                          color: _isFocused
                              ? Theme.of(context).primaryColor
                              : Colors.grey.shade600,
                          size: 22,
                        ),
                      );
              },
            ),
          ),
          // 输入框
          Expanded(
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              onChanged: _updateSuggestions,
              onSubmitted: _performSearch,
              style: const TextStyle(fontSize: 16),
              decoration: InputDecoration(
                hintText: '搜索动态、用户、钓点...',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 16,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 12,
                ),
              ),
            ),
          ),
          // 清除按钮
          if (_searchController.text.isNotEmpty)
            IconButton(
              icon: Icon(
                Icons.clear_rounded,
                color: Colors.grey.shade500,
                size: 20,
              ),
              onPressed: () {
                _searchController.clear();
                _updateSuggestions('');
              },
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36,
                minHeight: 36,
              ),
            ),
          // 搜索按钮
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _searchController.text.isNotEmpty ? _triggerSearch : null,
              borderRadius: const BorderRadius.horizontal(
                right: Radius.circular(22),
              ),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  '搜索',
                  style: TextStyle(
                    color: _searchController.text.isNotEmpty
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade400,
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernFilterBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          // 类型筛选
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: _searchTypes.map((type) {
                  final isSelected = _selectedType == type['key'];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ChoiceChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            type['icon'],
                            size: 16,
                            color: isSelected
                                ? Colors.white
                                : Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Text(type['name']),
                        ],
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedType = type['key'];
                        });
                        _performSearch(_searchController.text);
                      },
                      selectedColor: Theme.of(context).primaryColor,
                      backgroundColor: Colors.grey.shade100,
                      labelStyle: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          // 排序选择
          Container(
            height: 24,
            width: 1,
            color: Colors.grey.shade300,
            margin: const EdgeInsets.symmetric(horizontal: 8),
          ),
          _buildSortSelector(),
          const SizedBox(width: 16),
        ],
      ),
    );
  }

  Widget _buildSortSelector() {
    return PopupMenuButton<String>(
      onSelected: (value) {
        setState(() {
          _selectedSort = value;
        });
        _performSearch(_searchController.text);
      },
      itemBuilder: (context) => _sortTypes.map((sort) {
        return PopupMenuItem<String>(
          value: sort['key']!,
          child: Row(
            children: [
              Icon(
                _selectedSort == sort['key']
                    ? Icons.radio_button_checked_rounded
                    : Icons.radio_button_unchecked_rounded,
                size: 16,
                color: _selectedSort == sort['key']
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade400,
              ),
              const SizedBox(width: 8),
              Text(sort['name']!),
            ],
          ),
        );
      }).toList(),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      offset: const Offset(0, 8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort_rounded,
              size: 16,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              _sortTypes.firstWhere((e) => e['key'] == _selectedSort)['name']!,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 2),
            Icon(
              Icons.keyboard_arrow_down_rounded,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernEmptyState() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索历史
          if (_searchHistory.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.history_rounded,
                      size: 20,
                      color: Colors.grey.shade700,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '搜索历史',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: _clearSearchHistory,
                  child: Text(
                    '清空',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _searchHistory.take(10).map((keyword) {
                return Material(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                  child: InkWell(
                    onTap: () => _selectHistoryKeyword(keyword),
                    onLongPress: () => _deleteHistoryKeyword(keyword),
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            keyword,
                            style: TextStyle(
                              color: Colors.grey.shade700,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.close_rounded,
                            size: 16,
                            color: Colors.grey.shade500,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 24),
          ],

          // 热门搜索
          if (_hotKeywords.isNotEmpty) ...[
            Row(
              children: [
                Icon(
                  Icons.local_fire_department_rounded,
                  size: 20,
                  color: Colors.orange.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  '热门搜索',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _hotKeywords.asMap().entries.map((entry) {
                final index = entry.key;
                final keyword = entry.value;
                final isTop3 = index < 3;

                return Material(
                  color: isTop3 ? Colors.orange.shade50 : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                  child: InkWell(
                    onTap: () => _selectHistoryKeyword(keyword),
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (isTop3) ...[
                            Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.orange.shade600,
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Text(
                                  '${index + 1}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 6),
                          ],
                          Text(
                            keyword,
                            style: TextStyle(
                              color: isTop3
                                  ? Colors.orange.shade700
                                  : Colors.grey.shade700,
                              fontSize: 14,
                              fontWeight:
                                  isTop3 ? FontWeight.w500 : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildModernSuggestionsView() {
    return Container(
      color: Colors.white,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: _suggestions.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: Colors.grey.shade100,
          indent: 56,
        ),
        itemBuilder: (context, index) {
          final suggestion = _suggestions[index];
          return ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            leading: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color:
                    _getSuggestionColor(suggestion.type).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(18),
              ),
              child: Icon(
                _getSuggestionIcon(suggestion.type),
                size: 18,
                color: _getSuggestionColor(suggestion.type),
              ),
            ),
            title: RichText(
              text: TextSpan(
                children: _highlightSearchText(
                  suggestion.suggestion,
                  _searchController.text,
                ),
              ),
            ),
            subtitle: suggestion.extra != null
                ? Text(
                    suggestion.extra!,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey.shade600,
                    ),
                  )
                : null,
            trailing: Icon(
              Icons.arrow_forward_ios_rounded,
              size: 16,
              color: Colors.grey.shade400,
            ),
            onTap: () => _selectSuggestion(suggestion.suggestion),
          );
        },
      ),
    );
  }

  List<TextSpan> _highlightSearchText(String text, String keyword) {
    if (keyword.isEmpty) {
      return [
        TextSpan(text: text, style: const TextStyle(color: Colors.black87))
      ];
    }

    final List<TextSpan> spans = [];
    final lowerText = text.toLowerCase();
    final lowerKeyword = keyword.toLowerCase();
    int start = 0;

    while (true) {
      final index = lowerText.indexOf(lowerKeyword, start);
      if (index == -1) {
        spans.add(TextSpan(
          text: text.substring(start),
          style: const TextStyle(color: Colors.black87),
        ));
        break;
      }

      if (index > start) {
        spans.add(TextSpan(
          text: text.substring(start, index),
          style: const TextStyle(color: Colors.black87),
        ));
      }

      spans.add(TextSpan(
        text: text.substring(index, index + keyword.length),
        style: TextStyle(
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.bold,
        ),
      ));

      start = index + keyword.length;
    }

    return spans;
  }

  Widget _buildSkeletonLoader() {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 5,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Container(
                        height: 20,
                        decoration: BoxDecoration(
                          color: Colors.grey,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 40,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '未找到相关内容',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '试试调整搜索词或筛选条件',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          // 相关推荐
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Column(
              children: [
                Text(
                  '你可能想搜索：',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: ['路亚技巧', '鲤鱼钓法', '野钓装备'].map((keyword) {
                    return Material(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(16),
                      child: InkWell(
                        onTap: () => _selectHistoryKeyword(keyword),
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          child: Text(
                            keyword,
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSearchResults() {
    return RefreshIndicator(
      onRefresh: () async {
        await _performSearch(_searchController.text);
      },
      child: NotificationListener<ScrollNotification>(
        onNotification: (scrollInfo) {
          if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
              _searchResults.length < _totalCount &&
              !_isLoading) {
            _loadMoreResults();
          }
          return false;
        },
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _searchResults.length + (_isLoading ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == _searchResults.length) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            final result = _searchResults[index];
            return _buildModernSearchResultItem(result, index);
          },
        ),
      ),
    );
  }

  Widget _buildModernSearchResultItem(SearchResultVO result, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => _navigateToDetail(result),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部信息
                Row(
                  children: [
                    // 类型标签
                    if (result.type == 'moment' && result.momentType != null)
                      _buildMomentTypeLabel(result.momentType!)
                    else
                      _buildModernResultIcon(result.type),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            result.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              height: 1.3,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (result.authorName != null) ...[
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.person_outline_rounded,
                                  size: 14,
                                  color: Colors.grey.shade500,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  result.authorName!,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.grey.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                if (result.location != null) ...[
                                  const SizedBox(width: 12),
                                  Icon(
                                    Icons.location_on_outlined,
                                    size: 14,
                                    color: Colors.grey.shade500,
                                  ),
                                  const SizedBox(width: 4),
                                  Flexible(
                                    child: Text(
                                      result.location!,
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: Colors.grey.shade600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),

                // 内容摘要
                if (result.content != null && result.content!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Text(
                    result.content!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                // 图片预览
                if (result.images != null && result.images!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 100,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount:
                          result.images!.length > 3 ? 3 : result.images!.length,
                      itemBuilder: (context, imgIndex) {
                        return Container(
                          margin: EdgeInsets.only(
                            right: imgIndex < 2 ? 8 : 0,
                          ),
                          child: Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.network(
                                  result.images![imgIndex],
                                  width: 100,
                                  height: 100,
                                  fit: BoxFit.cover,
                                  loadingBuilder:
                                      (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Container(
                                      width: 100,
                                      height: 100,
                                      color: Colors.grey.shade200,
                                      child: const Center(
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      ),
                                    );
                                  },
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      width: 100,
                                      height: 100,
                                      color: Colors.grey.shade200,
                                      child: Icon(
                                        Icons.broken_image_rounded,
                                        color: Colors.grey.shade400,
                                      ),
                                    );
                                  },
                                ),
                              ),
                              if (imgIndex == 2 && result.images!.length > 3)
                                Positioned.fill(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color:
                                          Colors.black.withValues(alpha: 0.6),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Center(
                                      child: Text(
                                        '+${result.images!.length - 3}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],

                // 类型特定信息
                if (result.type == 'moment' && result.typeSpecificData != null)
                  _buildTypeSpecificInfo(
                      result.momentType, result.typeSpecificData!),

                // 底部操作栏
                const SizedBox(height: 12),
                _buildResultActionBar(result),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMomentTypeLabel(String momentType) {
    final typeConfig = _getMomentTypeConfig(momentType);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: typeConfig['color'].withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: typeConfig['color'].withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            typeConfig['icon'],
            size: 14,
            color: typeConfig['color'],
          ),
          const SizedBox(width: 4),
          Text(
            typeConfig['name'],
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: typeConfig['color'],
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getMomentTypeConfig(String type) {
    switch (type) {
      case 'fishing_catch':
        return {
          'name': '钓获',
          'icon': Icons.phishing,
          'color': Colors.blue.shade600,
        };
      case 'equipment':
        return {
          'name': '装备',
          'icon': Icons.construction,
          'color': Colors.orange.shade600,
        };
      case 'technique':
        return {
          'name': '技巧',
          'icon': Icons.lightbulb_outline,
          'color': Colors.green.shade600,
        };
      case 'question':
        return {
          'name': '问答',
          'icon': Icons.help_outline,
          'color': Colors.purple.shade600,
        };
      default:
        return {
          'name': '动态',
          'icon': Icons.article_rounded,
          'color': Colors.grey.shade600,
        };
    }
  }

  Widget _buildTypeSpecificInfo(String? momentType, Map<String, dynamic> data) {
    switch (momentType) {
      case 'fishing_catch':
        final fishes = data['caughtFishes'] as List?;
        if (fishes != null && fishes.isNotEmpty) {
          return Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              children: [
                Icon(Icons.phishing, size: 14, color: Colors.blue.shade400),
                const SizedBox(width: 4),
                Text(
                  '${fishes.length}种鱼获',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.blue.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          );
        }
        break;
      case 'equipment':
        final name = data['equipmentName'];
        if (name != null) {
          return Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              children: [
                Icon(Icons.construction,
                    size: 14, color: Colors.orange.shade400),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    name,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.orange.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          );
        }
        break;
      case 'technique':
        final name = data['techniqueName'];
        if (name != null) {
          return Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              children: [
                Icon(Icons.lightbulb_outline,
                    size: 14, color: Colors.green.shade400),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    name,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.green.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          );
        }
        break;
      case 'question':
        final title = data['questionTitle'];
        if (title != null) {
          return Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              children: [
                Icon(Icons.help_outline,
                    size: 14, color: Colors.purple.shade400),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.purple.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          );
        }
        break;
    }
    return const SizedBox.shrink();
  }

  Widget _buildModernResultIcon(String type) {
    IconData icon;
    Color color;

    switch (type) {
      case 'user':
        icon = Icons.person_rounded;
        color = Colors.green.shade600;
        break;
      case 'spot':
        icon = Icons.location_on_rounded;
        color = Colors.red.shade600;
        break;
      case 'moment':
        icon = Icons.article_rounded;
        color = Colors.blue.shade600;
        break;
      default:
        icon = Icons.search_rounded;
        color = Colors.grey.shade600;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        icon,
        size: 20,
        color: color,
      ),
    );
  }

  Widget _buildResultActionBar(SearchResultVO result) {
    return Row(
      children: [
        if (result.likeCount != null)
          _buildActionButton(
            icon: Icons.thumb_up_alt_outlined,
            count: result.likeCount!,
            color: Colors.grey.shade500,
          ),
        if (result.likeCount != null) const SizedBox(width: 16),
        if (result.commentCount != null)
          _buildActionButton(
            icon: Icons.comment_outlined,
            count: result.commentCount!,
            color: Colors.grey.shade500,
          ),
        if (result.commentCount != null) const SizedBox(width: 16),
        if (result.viewCount != null)
          _buildActionButton(
            icon: Icons.remove_red_eye_outlined,
            count: result.viewCount!,
            color: Colors.grey.shade500,
          ),
        const Spacer(),
        InkWell(
          onTap: () {
            // TODO: 分享功能
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Icon(
              Icons.share_rounded,
              size: 18,
              color: Colors.grey.shade500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required int count,
    required Color color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 4),
        Text(
          _formatCount(count),
          style: TextStyle(
            fontSize: 13,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _formatCount(int count) {
    if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    }
    return count.toString();
  }

  IconData _getSuggestionIcon(String type) {
    switch (type) {
      case 'user':
        return Icons.person_rounded;
      case 'spot':
        return Icons.location_on_rounded;
      case 'keyword':
        return Icons.search_rounded;
      default:
        return Icons.lightbulb_outline_rounded;
    }
  }

  Color _getSuggestionColor(String type) {
    switch (type) {
      case 'user':
        return Colors.green.shade500;
      case 'spot':
        return Colors.red.shade500;
      case 'keyword':
        return Colors.blue.shade500;
      default:
        return Colors.orange.shade500;
    }
  }

  void _navigateToDetail(SearchResultVO result) {
    debugPrint('=== CommunitySearchPage _navigateToDetail 调试信息 ===');
    debugPrint('结果类型: ${result.type}');
    debugPrint('结果ID: ${result.id}');
    debugPrint('结果ID类型: ${result.id.runtimeType}');
    debugPrint('结果标题: ${result.title}');

    try {
      // 根据不同类型跳转到对应详情页
      switch (result.type) {
        case 'moment':
          debugPrint('跳转到动态详情页面');
          context.push(AppRoutes.momentDetail, extra: {'momentId': result.id});
          break;
        case 'user':
          debugPrint('跳转到用户详情页面');
          context.push('${AppRoutes.profile}/${result.id}');
          break;
        case 'spot':
          debugPrint('跳转到钓点详情页面');
          context.push(AppRoutes.fishingSpotDetail, extra: {'spotId': result.id});
          break;
        default:
          debugPrint('未知的搜索结果类型: ${result.type}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('不支持的结果类型: ${result.type}')),
          );
      }
      debugPrint('路由跳转调用完成');
    } catch (e, stackTrace) {
      debugPrint('路由跳转错误: $e');
      debugPrint('错误堆栈: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('跳转失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
